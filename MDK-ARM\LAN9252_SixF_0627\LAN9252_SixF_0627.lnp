--cpu=Cortex-M33
"lan9252_sixf_0627\startup_stm32h563xx.o"
"lan9252_sixf_0627\app_freertos.o"
"lan9252_sixf_0627\stm32h5xx_it.o"
"lan9252_sixf_0627\stm32h5xx_hal_msp.o"
"lan9252_sixf_0627\stm32h5xx_hal_timebase_tim.o"
"lan9252_sixf_0627\adcadpt.o"
"lan9252_sixf_0627\appcfg.o"
"lan9252_sixf_0627\commuadpt.o"
"lan9252_sixf_0627\decplbyneuralnet.o"
"lan9252_sixf_0627\flashpersis_halg4.o"
"lan9252_sixf_0627\mbcommuadpt.o"
"lan9252_sixf_0627\rtmvlntpdo.o"
"lan9252_sixf_0627\synreplyv2.o"
"lan9252_sixf_0627\usrappmain.o"
"lan9252_sixf_0627\main.o"
"lan9252_sixf_0627\gpio.o"
"lan9252_sixf_0627\crc.o"
"lan9252_sixf_0627\dma.o"
"lan9252_sixf_0627\iwdg.o"
"lan9252_sixf_0627\spi.o"
"lan9252_sixf_0627\tim.o"
"lan9252_sixf_0627\usart.o"
"lan9252_sixf_0627\bsp_spiflash.o"
"lan9252_sixf_0627\el9800appl.o"
"lan9252_sixf_0627\el9800hw.o"
"lan9252_sixf_0627\stm32h5xx_hal_tim.o"
"lan9252_sixf_0627\stm32h5xx_hal_tim_ex.o"
"lan9252_sixf_0627\stm32h5xx_hal_cortex.o"
"lan9252_sixf_0627\stm32h5xx_hal_i2c.o"
"lan9252_sixf_0627\stm32h5xx_hal_i2c_ex.o"
"lan9252_sixf_0627\stm32h5xx_hal_dma.o"
"lan9252_sixf_0627\stm32h5xx_hal_dma_ex.o"
"lan9252_sixf_0627\stm32h5xx_hal_rcc.o"
"lan9252_sixf_0627\stm32h5xx_hal_rcc_ex.o"
"lan9252_sixf_0627\stm32h5xx_hal_flash.o"
"lan9252_sixf_0627\stm32h5xx_hal_flash_ex.o"
"lan9252_sixf_0627\stm32h5xx_hal_gpio.o"
"lan9252_sixf_0627\stm32h5xx_hal_pwr.o"
"lan9252_sixf_0627\stm32h5xx_hal_pwr_ex.o"
"lan9252_sixf_0627\stm32h5xx_hal.o"
"lan9252_sixf_0627\stm32h5xx_hal_exti.o"
"lan9252_sixf_0627\stm32h5xx_hal_icache.o"
"lan9252_sixf_0627\stm32h5xx_hal_spi.o"
"lan9252_sixf_0627\stm32h5xx_hal_spi_ex.o"
"lan9252_sixf_0627\stm32h5xx_hal_uart.o"
"lan9252_sixf_0627\stm32h5xx_hal_uart_ex.o"
"lan9252_sixf_0627\stm32h5xx_hal_crc.o"
"lan9252_sixf_0627\stm32h5xx_hal_iwdg.o"
"lan9252_sixf_0627\stm32h5xx_hal_crc_ex.o"
"lan9252_sixf_0627\system_stm32h5xx.o"
"lan9252_sixf_0627\croutine.o"
"lan9252_sixf_0627\event_groups.o"
"lan9252_sixf_0627\list.o"
"lan9252_sixf_0627\queue.o"
"lan9252_sixf_0627\stream_buffer.o"
"lan9252_sixf_0627\tasks.o"
"lan9252_sixf_0627\timers.o"
"lan9252_sixf_0627\port.o"
"lan9252_sixf_0627\portasm.o"
"lan9252_sixf_0627\cmsis_os2.o"
"lan9252_sixf_0627\heap_4.o"
"lan9252_sixf_0627\aoeappl.o"
"lan9252_sixf_0627\coeappl.o"
"lan9252_sixf_0627\ecatappl.o"
"lan9252_sixf_0627\foeappl.o"
"lan9252_sixf_0627\bootmode.o"
"lan9252_sixf_0627\diag.o"
"lan9252_sixf_0627\ecataoe.o"
"lan9252_sixf_0627\ecatcoe.o"
"lan9252_sixf_0627\ecateoe.o"
"lan9252_sixf_0627\ecatfoe.o"
"lan9252_sixf_0627\ecatslv.o"
"lan9252_sixf_0627\ecatsoe.o"
"lan9252_sixf_0627\emcy.o"
"lan9252_sixf_0627\eoeappl.o"
"lan9252_sixf_0627\mailbox.o"
"lan9252_sixf_0627\objdef.o"
"lan9252_sixf_0627\sdoserv.o"
--strict --scatter "LAN9252_SixF_0627\LAN9252_SixF_0627.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "LAN9252_SixF_0627.map" -o LAN9252_SixF_0627\LAN9252_SixF_0627.axf