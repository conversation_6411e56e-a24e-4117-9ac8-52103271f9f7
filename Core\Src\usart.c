/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    usart.c
  * @brief   This file provides code for the configuration
  *          of the USART instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "usart.h"
#include <string.h>

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

UART_HandleTypeDef huart1;

DMA_HandleTypeDef hdma_usart1_rx;
DMA_HandleTypeDef hdma_usart1_tx;


uint8_t TxBuffer[255];
uint8_t RxBuffer[255];
/* USART1 init function */

void MX_USART1_UART_Init(void)
{

  /* USER CODE BEGIN USART1_Init 0 */

  /* USER CODE END USART1_Init 0 */

  /* USER CODE BEGIN USART1_Init 1 */

  /* USER CODE END USART1_Init 1 */
  huart1.Instance = USART1;
  huart1.Init.BaudRate = 19200;
  huart1.Init.WordLength = UART_WORDLENGTH_8B;
  huart1.Init.StopBits = UART_STOPBITS_1;
  huart1.Init.Parity = UART_PARITY_NONE;
  huart1.Init.Mode = UART_MODE_TX_RX;
  huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart1.Init.OverSampling = UART_OVERSAMPLING_16;
  huart1.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
  huart1.Init.ClockPrescaler = UART_PRESCALER_DIV1;
  huart1.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
//	if (HAL_UART_Init(&huart1) != HAL_OK)
//	{
//    Error_Handler();
//	}
  // 如果PC13不支持USART1_DE，则使用普通UART初始化 + 手动控制
  if (HAL_UART_Init(&huart1) != HAL_OK)
  {
    Error_Handler();
  }
  // 注释掉RS485硬件控制
  // if (HAL_RS485Ex_Init(&huart1, UART_DE_POLARITY_HIGH, 1, 1) != HAL_OK)
  // {
  //   Error_Handler();
  // }
  if (HAL_UARTEx_SetTxFifoThreshold(&huart1, UART_TXFIFO_THRESHOLD_1_8) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_UARTEx_SetRxFifoThreshold(&huart1, UART_RXFIFO_THRESHOLD_1_8) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_UARTEx_DisableFifoMode(&huart1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART1_Init 2 */

  /* USER CODE END USART1_Init 2 */

}


void HAL_UART_MspInit(UART_HandleTypeDef* uartHandle)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};
  
  if(uartHandle->Instance==USART1)
  {
  /* USER CODE BEGIN USART1_MspInit 0 */

  /* USER CODE END USART1_MspInit 0 */
		
  /** Initializes the peripherals clocks
  */
    PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART1;
    PeriphClkInit.Usart1ClockSelection = RCC_USART1CLKSOURCE_PCLK2;
    if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
    {
      Error_Handler();
    }

    /* USART1 clock enable */
    __HAL_RCC_USART1_CLK_ENABLE();

    /* GPIO Ports Clock Enable */
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_GPIOC_CLK_ENABLE();
    /*USART1 GPIO Configuration    
    PB14     ------> USART1_TX
    PB15     ------> USART1_RX
    PC13     ------> USART1_EN
    */
		
    /* Configure PB14 (TX) and PB15 (RX) */
    GPIO_InitStruct.Pin = GPIO_PIN_14|GPIO_PIN_15;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF4_USART1;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    /* Configure PC13 (DE/EN) as GPIO output for manual RS485 control */
    GPIO_InitStruct.Pin = GPIO_PIN_13;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    
    // 初始化为接收模式
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET);

    /* USART1 DMA Init */
    /* USART1_RX Init - ʹ��Channel6 */
    hdma_usart1_rx.Instance = GPDMA1_Channel6;
    hdma_usart1_rx.Init.Request = GPDMA1_REQUEST_USART1_RX;
    hdma_usart1_rx.Init.BlkHWRequest = DMA_BREQ_SINGLE_BURST;
    hdma_usart1_rx.Init.Direction = DMA_PERIPH_TO_MEMORY;
    hdma_usart1_rx.Init.SrcInc = DMA_SINC_FIXED;
    hdma_usart1_rx.Init.DestInc = DMA_DINC_INCREMENTED;
    hdma_usart1_rx.Init.SrcDataWidth = DMA_SRC_DATAWIDTH_BYTE;
    hdma_usart1_rx.Init.DestDataWidth = DMA_DEST_DATAWIDTH_BYTE;
    hdma_usart1_rx.Init.Priority = DMA_HIGH_PRIORITY;
    hdma_usart1_rx.Init.SrcBurstLength = 1;
    hdma_usart1_rx.Init.DestBurstLength = 1;
    hdma_usart1_rx.Init.TransferAllocatedPort = DMA_SRC_ALLOCATED_PORT0|DMA_DEST_ALLOCATED_PORT1;
    hdma_usart1_rx.Init.TransferEventMode = DMA_TCEM_BLOCK_TRANSFER;
    hdma_usart1_rx.Init.Mode = DMA_NORMAL;
    if (HAL_DMA_Init(&hdma_usart1_rx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle,hdmarx,hdma_usart1_rx);

    /* USART1_TX Init - ʹ��Channel7 */
    hdma_usart1_tx.Instance = GPDMA1_Channel7;
    hdma_usart1_tx.Init.Request = GPDMA1_REQUEST_USART1_TX;
    hdma_usart1_tx.Init.BlkHWRequest = DMA_BREQ_SINGLE_BURST;
    hdma_usart1_tx.Init.Direction = DMA_MEMORY_TO_PERIPH;
    hdma_usart1_tx.Init.SrcInc = DMA_SINC_INCREMENTED;
    hdma_usart1_tx.Init.DestInc = DMA_DINC_FIXED;
    hdma_usart1_tx.Init.SrcDataWidth = DMA_SRC_DATAWIDTH_BYTE;
    hdma_usart1_tx.Init.DestDataWidth = DMA_DEST_DATAWIDTH_BYTE;
    hdma_usart1_tx.Init.Priority = DMA_HIGH_PRIORITY;
    hdma_usart1_tx.Init.SrcBurstLength = 1;
    hdma_usart1_tx.Init.DestBurstLength = 1;
    hdma_usart1_tx.Init.TransferAllocatedPort = DMA_SRC_ALLOCATED_PORT0|DMA_DEST_ALLOCATED_PORT1;
    hdma_usart1_tx.Init.TransferEventMode = DMA_TCEM_BLOCK_TRANSFER;
    hdma_usart1_tx.Init.Mode = DMA_NORMAL;
    if (HAL_DMA_Init(&hdma_usart1_tx) != HAL_OK)
    {
      Error_Handler();
    }

    __HAL_LINKDMA(uartHandle,hdmatx,hdma_usart1_tx);

    /* USART1 interrupt Init */
    HAL_NVIC_SetPriority(USART1_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(USART1_IRQn);
  /* USER CODE BEGIN USART1_MspInit 1 */

  /* USER CODE END USART1_MspInit 1 */
  }
}


void HAL_UART_MspDeInit(UART_HandleTypeDef* uartHandle)
{
  if(uartHandle->Instance==USART1)
  {
  /* USER CODE BEGIN USART1_MspDeInit 0 */

  /* USER CODE END USART1_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_USART1_CLK_DISABLE();

    /**USART1 GPIO Configuration    
    PB14     ------> USART1_TX
    PB15     ------> USART1_RX
    PC13     ------> USART1_DE
    */
    HAL_GPIO_DeInit(GPIOB, GPIO_PIN_14|GPIO_PIN_15);
    HAL_GPIO_DeInit(GPIOC, GPIO_PIN_13);

    /* USART1 DMA DeInit */
    HAL_DMA_DeInit(uartHandle->hdmarx);
    HAL_DMA_DeInit(uartHandle->hdmatx);

    /* USART1 interrupt Deinit */
    HAL_NVIC_DisableIRQ(USART1_IRQn);
  /* USER CODE BEGIN USART1_MspDeInit 1 */

  /* USER CODE END USART1_MspDeInit 1 */
  }
}


void Usart_SendData(uint8_t *data, uint8_t length) {
    // 进入发送模式
    RS485_TX_MODE();
    
    // 添加短暂延时，确保RS485收发器切换完成
    HAL_Delay(1);
    
    // 复制数据到发送缓冲区
    memcpy(TxBuffer, data, length);
    if (HAL_UART_Transmit(&huart1, data, length, HAL_MAX_DELAY) != HAL_OK) {
        // 处理错误，比如超时或硬件错误
        Error_Handler();
    }
    
    // 等待发送完成 - 使用更可靠的方法
    while (__HAL_UART_GET_FLAG(&huart1, UART_FLAG_TC) == RESET) {
        // 等待传输完成标志
    }
    
    // 额外延时确保最后一个字节完全发送
    HAL_Delay(2);
    
    // 切换回接收模式
    RS485_RX_MODE();
}



// 正确使用DMA的RS485发送函数
void Usart_SendData_DMA(uint8_t *data, uint8_t length) {
    // 进入发送模式
    RS485_TX_MODE();
    HAL_Delay(1);
    
    // 复制数据到发送缓冲区
    memcpy(TxBuffer, data, length);
    
    // 使用DMA发送
    if (HAL_UART_Transmit_DMA(&huart1, TxBuffer, length) != HAL_OK) {
        Error_Handler();
    }
    
    // 等待DMA传输完成
    while (HAL_UART_GetState(&huart1) != HAL_UART_STATE_READY) {
        // 等待DMA发送完成
        HAL_Delay(1); // 避免死循环，给系统一些时间
    }
    
    // 等待UART硬件发送完成
    while (__HAL_UART_GET_FLAG(&huart1, UART_FLAG_TC) == RESET) {
        // 等待传输完成标志
    }
    
    HAL_Delay(2);
    RS485_RX_MODE();
}
/* USER CODE BEGIN 1 */


/* USER CODE END 1 */
