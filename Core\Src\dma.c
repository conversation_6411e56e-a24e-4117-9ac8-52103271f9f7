/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    dma.c
  * @brief   This file provides code for the configuration
  *          of all the requested memory to memory DMA transfers.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "dma.h"

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/*----------------------------------------------------------------------------*/
/* Configure DMA                                                              */
/*----------------------------------------------------------------------------*/

/* USER CODE BEGIN 1 */

/* USER CODE END 1 */

/**
  * Enable DMA controller clock
  */
void MX_DMA_Init(void)
{

  /* DMA controller clock enable */
	__HAL_RCC_GPDMA1_CLK_ENABLE();
//	__HAL_RCC_GPDMA2_CLK_ENABLE();

	
	  HAL_NVIC_SetPriority(GPDMA1_Channel0_IRQn, 8, 0);
    HAL_NVIC_EnableIRQ(GPDMA1_Channel0_IRQn);
    HAL_NVIC_SetPriority(GPDMA1_Channel1_IRQn, 8, 0);
    HAL_NVIC_EnableIRQ(GPDMA1_Channel1_IRQn);
	
    HAL_NVIC_SetPriority(GPDMA1_Channel2_IRQn, 8, 0);
    HAL_NVIC_EnableIRQ(GPDMA1_Channel2_IRQn);
    HAL_NVIC_SetPriority(GPDMA1_Channel3_IRQn, 8, 0);
    HAL_NVIC_EnableIRQ(GPDMA1_Channel3_IRQn);
	
	  HAL_NVIC_SetPriority(GPDMA1_Channel4_IRQn, 8, 0);
    HAL_NVIC_EnableIRQ(GPDMA1_Channel4_IRQn);
    HAL_NVIC_SetPriority(GPDMA1_Channel5_IRQn, 8, 0);
    HAL_NVIC_EnableIRQ(GPDMA1_Channel5_IRQn);
	
//  /* DMA2_Channel1_IRQn interrupt configuration */
//  HAL_NVIC_SetPriority(DMA2_Channel1_IRQn, 6, 0);
//  HAL_NVIC_EnableIRQ(DMA2_Channel1_IRQn);
//  /* DMA2_Channel2_IRQn interrupt configuration */
//  HAL_NVIC_SetPriority(DMA2_Channel2_IRQn, 6, 0);
//  HAL_NVIC_EnableIRQ(DMA2_Channel2_IRQn);

}

