<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [LAN9252_SixF_0627\LAN9252_SixF_0627.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image LAN9252_SixF_0627\LAN9252_SixF_0627.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6230001: Last Updated: Wed Aug 13 22:33:39 2025
<BR><P>
<H3>Maximum Stack Usage =        728 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; MX_SPI3_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[6]">SecureFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">SecureFault_Handler</a><BR>
 <LI><a href="#[2f]">ADC1_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2f]">ADC1_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[2f]">ADC1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[4f]">ADC2_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[8c]">AdcAdpt_AddSingleChlVal32</a> from adcadpt.o(.text.AdcAdpt_AddSingleChlVal32) referenced 2 times from adcadpt.o(.text.AdcAdpt_InitAdcs)
 <LI><a href="#[4]">BusFault_Handler</a> from stm32h5xx_it.o(.text.BusFault_Handler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[7e]">CEC_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[79]">CORDIC_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[55]">CRS_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[30]">DAC1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[73]">DCACHE1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[76]">DCMI_PSSI_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[7b]">DTS_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[8]">DebugMon_Handler</a> from stm32h5xx_it.o(.text.DebugMon_Handler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[74]">ETH_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[75]">ETH_WKUP_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[16]">EXTI0_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[20]">EXTI10_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[21]">EXTI11_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[22]">EXTI12_IRQHandler</a> from stm32h5xx_it.o(.text.EXTI12_IRQHandler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[23]">EXTI13_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[24]">EXTI14_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[25]">EXTI15_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[17]">EXTI1_IRQHandler</a> from stm32h5xx_it.o(.text.EXTI1_IRQHandler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[18]">EXTI2_IRQHandler</a> from stm32h5xx_it.o(.text.EXTI2_IRQHandler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[19]">EXTI3_IRQHandler</a> from stm32h5xx_it.o(.text.EXTI3_IRQHandler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[1a]">EXTI4_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[1b]">EXTI5_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[1c]">EXTI6_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[1d]">EXTI7_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[1e]">EXTI8_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[1f]">EXTI9_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[31]">FDCAN1_IT0_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[32]">FDCAN1_IT1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[77]">FDCAN2_IT0_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[78]">FDCAN2_IT1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[11]">FLASH_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[12]">FLASH_S_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[7a]">FMAC_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[57]">FMC_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[71]">FPU_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[26]">GPDMA1_Channel0_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[27]">GPDMA1_Channel1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[28]">GPDMA1_Channel2_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[29]">GPDMA1_Channel3_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[2a]">GPDMA1_Channel4_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[2b]">GPDMA1_Channel5_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[2c]">GPDMA1_Channel6_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[2d]">GPDMA1_Channel7_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[64]">GPDMA2_Channel0_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[65]">GPDMA2_Channel1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[66]">GPDMA2_Channel2_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[67]">GPDMA2_Channel3_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[68]">GPDMA2_Channel4_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[69]">GPDMA2_Channel5_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[6a]">GPDMA2_Channel6_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[6b]">GPDMA2_Channel7_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[13]">GTZC_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[7d]">HASH_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from stm32h5xx_it.o(.text.HardFault_Handler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[3e]">I2C1_ER_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[3d]">I2C1_EV_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[40]">I2C2_ER_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[3f]">I2C2_EV_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[5b]">I2C3_ER_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[5a]">I2C3_EV_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[85]">I2C4_ER_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[84]">I2C4_EV_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[83]">I3C1_ER_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[82]">I3C1_EV_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[72]">ICACHE_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[2e]">IWDG_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[4a]">LPTIM1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[50]">LPTIM2_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[86]">LPTIM3_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[87]">LPTIM4_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[88]">LPTIM5_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[89]">LPTIM6_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[49]">LPUART1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from stm32h5xx_it.o(.text.MemManage_Handler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[92]">ModbusRtuCalCrc16</a> from mbcommuadpt.o(.text.ModbusRtuCalCrc16) referenced 2 times from mbcommuadpt.o(.text.MBCommuAdpt_Init)
 <LI><a href="#[92]">ModbusRtuCalCrc16</a> from mbcommuadpt.o(.text.ModbusRtuCalCrc16) referenced 2 times from mbcommuadpt.o(.text.ModbusRtuSlave_Init)
 <LI><a href="#[9a]">ModbusRtuSlave_HandleRecvedPack</a> from mbcommuadpt.o(.text.ModbusRtuSlave_HandleRecvedPack) referenced 2 times from mbcommuadpt.o(.text.ModbusRtuSlave_RegisterCommRecver)
 <LI><a href="#[1]">NMI_Handler</a> from stm32h5xx_it.o(.text.NMI_Handler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[58]">OCTOSPI1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[94]">OnSlvReadAIs</a> from mbcommuadpt.o(.text.OnSlvReadAIs) referenced 2 times from mbcommuadpt.o(.text.MBCommuAdpt_Init)
 <LI><a href="#[97]">OnSlvReadCoils</a> from mbcommuadpt.o(.text.OnSlvReadCoils) referenced 2 times from mbcommuadpt.o(.text.MBCommuAdpt_Init)
 <LI><a href="#[96]">OnSlvReadDIs</a> from mbcommuadpt.o(.text.OnSlvReadDIs) referenced 2 times from mbcommuadpt.o(.text.MBCommuAdpt_Init)
 <LI><a href="#[95]">OnSlvReadHoldReg</a> from mbcommuadpt.o(.text.OnSlvReadHoldReg) referenced 2 times from mbcommuadpt.o(.text.MBCommuAdpt_Init)
 <LI><a href="#[9e]">OnTxAdsComplete</a> from synreplyv2.o(.text.OnTxAdsComplete) referenced 2 times from synreplyv2.o(.text.SynReply_Init)
 <LI><a href="#[98]">OnWriteCoils</a> from mbcommuadpt.o(.text.OnWriteCoils) referenced 2 times from mbcommuadpt.o(.text.MBCommuAdpt_Init)
 <LI><a href="#[99]">OnWriteHoldRegs</a> from mbcommuadpt.o(.text.OnWriteHoldRegs) referenced 2 times from mbcommuadpt.o(.text.MBCommuAdpt_Init)
 <LI><a href="#[c]">PVD_AVD_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[9]">PendSV_Handler</a> from portasm.o(.text.PendSV_Handler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[10]">RAMCFG_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[14]">RCC_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[15]">RCC_S_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[7c]">RNG_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[d]">RTC_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[e]">RTC_S_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[62]">SAI1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[63]">SAI2_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[59]">SDMMC1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[70]">SDMMC2_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[41]">SPI1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[42]">SPI2_IRQHandler</a> from stm32h5xx_it.o(.text.SPI2_IRQHandler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[43]">SPI3_IRQHandler</a> from stm32h5xx_it.o(.text.SPI3_IRQHandler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[5c]">SPI4_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[5d]">SPI5_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[5e]">SPI6_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[8d]">SPI_DMAAbortOnError</a> from stm32h5xx_hal_spi.o(.text.SPI_DMAAbortOnError) referenced 4 times from stm32h5xx_hal_spi.o(.text.HAL_SPI_IRQHandler)
 <LI><a href="#[7]">SVC_Handler</a> from portasm.o(.text.SVC_Handler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[6]">SecureFault_Handler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[9c]">SynReply_OnRx</a> from synreplyv2.o(.text.SynReply_OnRx) referenced 2 times from synreplyv2.o(.text.SynReply_Init)
 <LI><a href="#[9d]">SynReply_SimpPreChk</a> from synreplyv2.o(.text.SynReply_SimpPreChk) referenced 2 times from synreplyv2.o(.text.SynReply_Init)
 <LI><a href="#[a]">SysTick_Handler</a> from cmsis_os2.o(.text.SysTick_Handler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[8a]">SystemInit</a> from system_stm32h5xx.o(.text.SystemInit) referenced from startup_stm32h563xx.o(.text)
 <LI><a href="#[f]">TAMP_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[7f]">TIM12_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[80]">TIM13_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[81]">TIM14_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[51]">TIM15_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[52]">TIM16_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[53]">TIM17_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[33]">TIM1_BRK_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[36]">TIM1_CC_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[35]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[34]">TIM1_UP_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[37]">TIM2_IRQHandler</a> from el9800hw.o(.text.TIM2_IRQHandler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[38]">TIM3_IRQHandler</a> from stm32h5xx_it.o(.text.TIM3_IRQHandler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[39]">TIM4_IRQHandler</a> from stm32h5xx_it.o(.text.TIM4_IRQHandler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[3a]">TIM5_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[3b]">TIM6_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[3c]">TIM7_IRQHandler</a> from stm32h5xx_it.o(.text.TIM7_IRQHandler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[4b]">TIM8_BRK_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[4e]">TIM8_CC_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[4d]">TIM8_TRG_COM_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[4c]">TIM8_UP_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[6f]">UART12_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[47]">UART4_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[48]">UART5_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[6c]">UART7_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[6d]">UART8_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[6e]">UART9_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[8e]">UART_DMAAbortOnError</a> from stm32h5xx_hal_uart.o(.text.UART_DMAAbortOnError) referenced 2 times from stm32h5xx_hal_uart.o(.text.HAL_UART_IRQHandler)
 <LI><a href="#[91]">UART_DMAError</a> from stm32h5xx_hal_uart.o(.text.UART_DMAError) referenced 2 times from stm32h5xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
 <LI><a href="#[91]">UART_DMAError</a> from stm32h5xx_hal_uart.o(.text.UART_DMAError) referenced 2 times from stm32h5xx_hal_uart.o(.text.UART_Start_Receive_DMA)
 <LI><a href="#[9f]">UART_DMAReceiveCplt</a> from stm32h5xx_hal_uart.o(.text.UART_DMAReceiveCplt) referenced 2 times from stm32h5xx_hal_uart.o(.text.UART_Start_Receive_DMA)
 <LI><a href="#[a0]">UART_DMARxHalfCplt</a> from stm32h5xx_hal_uart.o(.text.UART_DMARxHalfCplt) referenced 2 times from stm32h5xx_hal_uart.o(.text.UART_Start_Receive_DMA)
 <LI><a href="#[8f]">UART_DMATransmitCplt</a> from stm32h5xx_hal_uart.o(.text.UART_DMATransmitCplt) referenced 2 times from stm32h5xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
 <LI><a href="#[90]">UART_DMATxHalfCplt</a> from stm32h5xx_hal_uart.o(.text.UART_DMATxHalfCplt) referenced 2 times from stm32h5xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
 <LI><a href="#[56]">UCPD1_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[60]">USART10_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[61]">USART11_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[44]">USART1_IRQHandler</a> from stm32h5xx_it.o(.text.USART1_IRQHandler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[45]">USART2_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[46]">USART3_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[5f]">USART6_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[54]">USB_DRD_FS_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[93]">UartWrite</a> from mbcommuadpt.o(.text.UartWrite) referenced 2 times from mbcommuadpt.o(.text.MBCommuAdpt_Init)
 <LI><a href="#[5]">UsageFault_Handler</a> from stm32h5xx_it.o(.text.UsageFault_Handler) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[b]">WWDG_IRQHandler</a> from startup_stm32h563xx.o(.text) referenced from startup_stm32h563xx.o(RESET)
 <LI><a href="#[8b]">__main</a> from __main.o(!!!main) referenced from startup_stm32h563xx.o(.text)
 <LI><a href="#[9b]">vPortSVCHandler_C</a> from port.o(.text.vPortSVCHandler_C) referenced from portasm.o(.text.SVC_Handler)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[8b]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(.text)
</UL>
<P><STRONG><a name="[a2]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[a4]"></a>__scatterload_rt2</STRONG> (Thumb, 84 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[1b3]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[1b4]"></a>__scatterload_loop</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[1b5]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[1b6]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[1b7]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, __scatter.o(!!handler_null), UNUSED)

<P><STRONG><a name="[1b8]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[aa]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[a5]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[1b9]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[1ba]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[1bb]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[1bc]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[1bd]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000034))

<P><STRONG><a name="[1be]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[1bf]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[1c0]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[1c1]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[1c2]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[1c3]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[1c4]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[1c5]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[1c6]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[1c7]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000006))

<P><STRONG><a name="[1c8]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000010))

<P><STRONG><a name="[1c9]"></a>__rt_lib_init_relocate_pie_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[1ca]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000035))

<P><STRONG><a name="[1cb]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[1cc]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000027))

<P><STRONG><a name="[1cd]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[af]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[1ce]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[1cf]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[1d0]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[1d1]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[1d2]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[1d3]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[1d4]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[a3]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[1d5]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[a7]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[a9]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[1d6]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[ab]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 728 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; MX_SPI3_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[1d7]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[b9]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[ae]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[1d8]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[b0]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SecureFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SecureFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SecureFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>ADC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>ADC2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[7e]"></a>CEC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[79]"></a>CORDIC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>CRS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>DAC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[73]"></a>DCACHE1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[76]"></a>DCMI_PSSI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[7b]"></a>DTS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[74]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>EXTI10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXTI13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>EXTI14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXTI15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>EXTI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>EXTI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>EXTI7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>EXTI8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>EXTI9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>FDCAN1_IT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>FDCAN1_IT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[77]"></a>FDCAN2_IT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[78]"></a>FDCAN2_IT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>FLASH_S_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>FMAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[71]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>GPDMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>GPDMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>GPDMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>GPDMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>GPDMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>GPDMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>GPDMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>GPDMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>GPDMA2_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>GPDMA2_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>GPDMA2_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>GPDMA2_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>GPDMA2_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>GPDMA2_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>GPDMA2_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>GPDMA2_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>GTZC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[7d]"></a>HASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[85]"></a>I2C4_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[84]"></a>I2C4_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[83]"></a>I3C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[82]"></a>I3C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[72]"></a>ICACHE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>IWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>LPTIM1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>LPTIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[86]"></a>LPTIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[87]"></a>LPTIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[88]"></a>LPTIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[89]"></a>LPTIM6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>LPUART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>OCTOSPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>PVD_AVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>RAMCFG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>RCC_S_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>RTC_S_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>SAI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>SDMMC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[70]"></a>SDMMC2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>TAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[80]"></a>TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[81]"></a>TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>TIM15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>TIM16_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>TIM17_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>TIM8_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>TIM8_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>TIM8_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[6f]"></a>UART12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[6e]"></a>UART9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>UCPD1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>USART10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>USART11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>USB_DRD_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[b8]"></a>__user_initial_stackheap</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, startup_stm32h563xx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[178]"></a>__aeabi_uldivmod</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, lludivv7m.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[1d9]"></a>_ll_udiv</STRONG> (Thumb, 240 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)

<P><STRONG><a name="[dd]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_StartAdc0InterruptTest
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendData
</UL>

<P><STRONG><a name="[b2]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[1da]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[16f]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[129]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[1db]"></a>__rt_memclr_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[1dc]"></a>_memset_w</STRONG> (Thumb, 74 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[1dd]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[1de]"></a>__rt_heap_escrow</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[1df]"></a>__rt_heap_expand</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[b4]"></a>__read_errno</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, _rserrno.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>

<P><STRONG><a name="[b6]"></a>__set_errno</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, _rserrno.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __set_errno
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[b3]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[1e0]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1e1]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1e2]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[1e3]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[b7]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[1e4]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[b5]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>

<P><STRONG><a name="[1e5]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[1e6]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[1e7]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1e8]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[1e9]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[a8]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[ad]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[b1]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[ba]"></a>AD4170EndIrq</STRONG> (Thumb, 242 bytes, Stack size 24 bytes, adcadpt.o(.text.AD4170EndIrq))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = AD4170EndIrq &rArr; ADC0_ConfigPC2_AsEXTI2 &rArr; HAL_NVIC_EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_ConfigPC2_AsEXTI2
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipHalSpiTxRxCpltCallback
</UL>

<P><STRONG><a name="[c1]"></a>ADC0_EXTI2_Configuration</STRONG> (Thumb, 102 bytes, Stack size 40 bytes, gpio.o(.text.ADC0_EXTI2_Configuration))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = ADC0_EXTI2_Configuration &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_EXTI_Init
</UL>

<P><STRONG><a name="[c3]"></a>ADC_EXTI_Enable</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, gpio.o(.text.ADC_EXTI_Enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ADC_EXTI_Enable &rArr; HAL_NVIC_EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_InitAdcs
</UL>

<P><STRONG><a name="[c4]"></a>ADC_EXTI_Init</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, gpio.o(.text.ADC_EXTI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = ADC_EXTI_Init &rArr; ADC0_EXTI2_Configuration &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_EXTI2_Configuration
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_InitAdcs
</UL>

<P><STRONG><a name="[cc]"></a>AdcAdpt_Adc0TestProcess</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, adcadpt.o(.text.AdcAdpt_Adc0TestProcess))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = AdcAdpt_Adc0TestProcess &rArr; AdcAdpt_SendAdc0DataVia485 &rArr; Usart_SendData &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_SendAdc0DataVia485
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8c]"></a>AdcAdpt_AddSingleChlVal32</STRONG> (Thumb, 360 bytes, Stack size 32 bytes, adcadpt.o(.text.AdcAdpt_AddSingleChlVal32))
<BR><BR>[Stack]<UL><LI>Max Depth = 448<LI>Call Chain = AdcAdpt_AddSingleChlVal32 &rArr; AdcAdpt_OnAllLgChlsCod16 &rArr; DecplNN_Calc &rArr; TanSigMatf32 &rArr; __hardfp_exp &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_OnAllLgChlsCod16
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Filt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> adcadpt.o(.text.AdcAdpt_InitAdcs)
</UL>
<P><STRONG><a name="[17e]"></a>AdcAdpt_AdjCodZeroOffsetByActCods</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, adcadpt.o(.text.AdcAdpt_AdjCodZeroOffsetByActCods))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AdcAdpt_AdjCodZeroOffsetByActCods
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmWrite
</UL>

<P><STRONG><a name="[180]"></a>AdcAdpt_AdjCodZeroOffsetByCur2ActCodDiffs</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, adcadpt.o(.text.AdcAdpt_AdjCodZeroOffsetByCur2ActCodDiffs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AdcAdpt_AdjCodZeroOffsetByCur2ActCodDiffs
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmWrite
</UL>

<P><STRONG><a name="[181]"></a>AdcAdpt_AdjValOffsetByActVals</STRONG> (Thumb, 168 bytes, Stack size 8 bytes, adcadpt.o(.text.AdcAdpt_AdjValOffsetByActVals))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AdcAdpt_AdjValOffsetByActVals
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmWrite
</UL>

<P><STRONG><a name="[183]"></a>AdcAdpt_AdjValOffsetByCur2ActValDiffs</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, adcadpt.o(.text.AdcAdpt_AdjValOffsetByCur2ActValDiffs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AdcAdpt_AdjValOffsetByCur2ActValDiffs
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmWrite
</UL>

<P><STRONG><a name="[dc]"></a>AdcAdpt_EnableAdc0Test</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, adcadpt.o(.text.AdcAdpt_EnableAdc0Test))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_StartAdc0InterruptTest
</UL>

<P><STRONG><a name="[d0]"></a>AdcAdpt_InitAdcs</STRONG> (Thumb, 452 bytes, Stack size 96 bytes, adcadpt.o(.text.AdcAdpt_InitAdcs))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = AdcAdpt_InitAdcs &rArr; AdcChipInit &rArr; ad4170_read_register16 &rArr; ad4170_spi_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_EXTI_Enable
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_EXTI_Init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipStart
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipInit
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipPrmPreIni
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipPrepHdlsALst
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ApplyChipConfigsToHandles
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_ConfigPC2_AsSPI_MISO
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrAppMain_InitHw
</UL>

<P><STRONG><a name="[cf]"></a>AdcAdpt_OnAllLgChlsCod16</STRONG> (Thumb, 1750 bytes, Stack size 104 bytes, adcadpt.o(.text.AdcAdpt_OnAllLgChlsCod16))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = AdcAdpt_OnAllLgChlsCod16 &rArr; DecplNN_Calc &rArr; TanSigMatf32 &rArr; __hardfp_exp &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ValDivision_GetDivedFloat
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DecplNN_Calc
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Matrix_Multiply
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_AddSingleChlVal32
</UL>

<P><STRONG><a name="[17f]"></a>AdcAdpt_RstCodZeroOffset</STRONG> (Thumb, 60 bytes, Stack size 4 bytes, adcadpt.o(.text.AdcAdpt_RstCodZeroOffset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = AdcAdpt_RstCodZeroOffset
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmWrite
</UL>

<P><STRONG><a name="[182]"></a>AdcAdpt_RstValZeroOffset</STRONG> (Thumb, 70 bytes, Stack size 4 bytes, adcadpt.o(.text.AdcAdpt_RstValZeroOffset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = AdcAdpt_RstValZeroOffset
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmWrite
</UL>

<P><STRONG><a name="[168]"></a>AdcAdpt_SetNewBuf</STRONG> (Thumb, 106 bytes, Stack size 28 bytes, adcadpt.o(.text.AdcAdpt_SetNewBuf))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = AdcAdpt_SetNewBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SynReply_OnRx
</UL>

<P><STRONG><a name="[db]"></a>AdcAdpt_StartAdc0InterruptTest</STRONG> (Thumb, 40 bytes, Stack size 48 bytes, adcadpt.o(.text.AdcAdpt_StartAdc0InterruptTest))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = AdcAdpt_StartAdc0InterruptTest &rArr; Usart_SendData &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendData
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_EnableAdc0Test
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[de]"></a>AdcChipHalSpiTxRxCpltCallback</STRONG> (Thumb, 148 bytes, Stack size 24 bytes, adcadpt.o(.text.AdcChipHalSpiTxRxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = AdcChipHalSpiTxRxCpltCallback &rArr; AD4170EndIrq &rArr; ADC0_ConfigPC2_AsEXTI2 &rArr; HAL_NVIC_EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_DMAStop
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD4170EndIrq
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxRxCpltCallback
</UL>

<P><STRONG><a name="[d5]"></a>AdcChipInit</STRONG> (Thumb, 630 bytes, Stack size 80 bytes, adcadpt.o(.text.AdcChipInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = AdcChipInit &rArr; ad4170_read_register16 &rArr; ad4170_spi_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_read_register16
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_write
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_write_register16
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_read_register
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_write_register
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_DisableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_InitAdcs
</UL>

<P><STRONG><a name="[d2]"></a>AdcChipPrepHdlsALst</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, adcadpt.o(.text.AdcChipPrepHdlsALst))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AdcChipPrepHdlsALst
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_InitAdcs
</UL>

<P><STRONG><a name="[d3]"></a>AdcChipPrmPreIni</STRONG> (Thumb, 236 bytes, Stack size 48 bytes, adcadpt.o(.text.AdcChipPrmPreIni))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = AdcChipPrmPreIni
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_InitAdcs
</UL>

<P><STRONG><a name="[d6]"></a>AdcChipStart</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, adcadpt.o(.text.AdcChipStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = AdcChipStart &rArr; ad4170_write_register16 &rArr; ad4170_spi_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_write_register16
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_InitAdcs
</UL>

<P><STRONG><a name="[e7]"></a>AppCfg_Default</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, appcfg.o(.text.AppCfg_Default))
<BR><BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AppCfg_Init
</UL>

<P><STRONG><a name="[e5]"></a>AppCfg_Init</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, appcfg.o(.text.AppCfg_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = AppCfg_Init &rArr; InitPrmItmArr
</UL>
<BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AppCfg_Default
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;InitPrmItmArr
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrAppMain_InitHw
</UL>

<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32h5xx_it.o(.text.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[145]"></a>CommuAdpt_Exe</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, commuadpt.o(.text.CommuAdpt_Exe))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CommuAdpt_Exe
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[ea]"></a>CommuAdpt_Init</STRONG> (Thumb, 142 bytes, Stack size 8 bytes, commuadpt.o(.text.CommuAdpt_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = CommuAdpt_Init &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SynReply_Init
</UL>

<P><STRONG><a name="[164]"></a>CommuAdpt_RegRxClb</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, commuadpt.o(.text.CommuAdpt_RegRxClb))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = CommuAdpt_RegRxClb
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SynReply_Init
</UL>

<P><STRONG><a name="[165]"></a>CommuAdpt_RegRxPreFiltClb</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, commuadpt.o(.text.CommuAdpt_RegRxPreFiltClb))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = CommuAdpt_RegRxPreFiltClb
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SynReply_Init
</UL>

<P><STRONG><a name="[166]"></a>CommuAdpt_RegTxedClb</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, commuadpt.o(.text.CommuAdpt_RegTxedClb))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = CommuAdpt_RegTxedClb
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SynReply_Init
</UL>

<P><STRONG><a name="[ec]"></a>CommuAdpt_StaticWrite</STRONG> (Thumb, 174 bytes, Stack size 24 bytes, commuadpt.o(.text.CommuAdpt_StaticWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = CommuAdpt_StaticWrite &rArr; HAL_UART_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmWrite
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmRead
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UartWrite
</UL>

<P><STRONG><a name="[f1]"></a>DC_CheckWatchdog</STRONG> (Thumb, 422 bytes, Stack size 0 bytes, ecatslv.o(.text.DC_CheckWatchdog))
<BR><BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ECAT_CheckTimer
</UL>

<P><STRONG><a name="[8]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32h5xx_it.o(.text.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[d8]"></a>DecplNN_Calc</STRONG> (Thumb, 382 bytes, Stack size 152 bytes, decplbyneuralnet.o(.text.DecplNN_Calc))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = DecplNN_Calc &rArr; TanSigMatf32 &rArr; __hardfp_exp &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Matrix_Add
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TanSigMatf32
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Matrix_Multiply
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_OnAllLgChlsCod16
</UL>

<P><STRONG><a name="[f0]"></a>ECAT_CheckTimer</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, ecatappl.o(.text.ECAT_CheckTimer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ECAT_CheckTimer
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DC_CheckWatchdog
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[22]"></a>EXTI12_IRQHandler</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, stm32h5xx_it.o(.text.EXTI12_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = EXTI12_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Falling_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32h5xx_it.o(.text.EXTI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = EXTI1_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Falling_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32h5xx_it.o(.text.EXTI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = EXTI2_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Falling_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, stm32h5xx_it.o(.text.EXTI3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = EXTI3_IRQHandler &rArr; HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Falling_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[12a]"></a>Error_Handler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, main.o(.text.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI3_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendData
</UL>

<P><STRONG><a name="[e9]"></a>HAL_CRC_Calculate</STRONG> (Thumb, 152 bytes, Stack size 32 bytes, stm32h5xx_hal_crc.o(.text.HAL_CRC_Calculate))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_CRC_Calculate &rArr; CRC_Handle_8
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC_Handle_16
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC_Handle_8
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmWrite
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmRead
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ChkSum
</UL>

<P><STRONG><a name="[f5]"></a>HAL_DMAEx_List_Start_IT</STRONG> (Thumb, 326 bytes, Stack size 32 bytes, stm32h5xx_hal_dma_ex.o(.text.HAL_DMAEx_List_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_DMAEx_List_Start_IT &rArr; DMA_List_GetCLLRNodeInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_List_GetCLLRNodeInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit_DMA
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>

<P><STRONG><a name="[f7]"></a>HAL_DMA_Abort</STRONG> (Thumb, 278 bytes, Stack size 24 bytes, stm32h5xx_hal_dma.o(.text.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortReceive
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Abort
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[125]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32h5xx_hal_dma.o(.text.HAL_DMA_Abort_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_DMA_Abort_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
</UL>

<P><STRONG><a name="[11c]"></a>HAL_DMA_GetError</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, stm32h5xx_hal_dma.o(.text.HAL_DMA_GetError))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_DMA_GetError
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortReceive
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Abort
</UL>

<P><STRONG><a name="[f9]"></a>HAL_DMA_Init</STRONG> (Thumb, 960 bytes, Stack size 24 bytes, stm32h5xx_hal_dma.o(.text.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_DMA_Init &rArr; DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Init
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>

<P><STRONG><a name="[fb]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 226 bytes, Stack size 32 bytes, stm32h5xx_hal_dma.o(.text.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit_DMA
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>

<P><STRONG><a name="[d4]"></a>HAL_Delay</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, stm32h5xx_hal.o(.text.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendData
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_InitAdcs
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipStart
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipInit
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_read
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_write
</UL>

<P><STRONG><a name="[fe]"></a>HAL_GPIO_EXTI_Falling_Callback</STRONG> (Thumb, 10 bytes, Stack size 4 bytes, stm32h5xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Falling_Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_GPIO_EXTI_Falling_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>

<P><STRONG><a name="[f2]"></a>HAL_GPIO_EXTI_IRQHandler</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, stm32h5xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_GPIO_EXTI_IRQHandler &rArr; HAL_GPIO_EXTI_Falling_Callback
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Falling_Callback
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_Rising_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI12_IRQHandler
<LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI2_IRQHandler
<LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI3_IRQHandler
<LI><a href="#[17]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI1_IRQHandler
</UL>

<P><STRONG><a name="[fd]"></a>HAL_GPIO_EXTI_Rising_Callback</STRONG> (Thumb, 10 bytes, Stack size 4 bytes, stm32h5xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Rising_Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_GPIO_EXTI_Rising_Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_EXTI_IRQHandler
</UL>

<P><STRONG><a name="[bd]"></a>HAL_GPIO_Init</STRONG> (Thumb, 746 bytes, Stack size 20 bytes, stm32h5xx_hal_gpio.o(.text.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_EXTI2_Configuration
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_ConfigPC2_AsEXTI2
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_ConfigPC2_AsSPI_MISO
</UL>

<P><STRONG><a name="[bb]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32h5xx_hal_gpio.o(.text.HAL_GPIO_WritePin))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendData
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD4170EndIrq
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipInit
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_read
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_write
</UL>

<P><STRONG><a name="[f8]"></a>HAL_GetTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32h5xx_hal.o(.text.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCEx_PLL3_Config
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCEx_PLL2_Config
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitOnFlagUntilTimeout
</UL>

<P><STRONG><a name="[141]"></a>HAL_IncTick</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32h5xx_hal.o(.text.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
</UL>

<P><STRONG><a name="[ff]"></a>HAL_Init</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, stm32h5xx_hal.o(.text.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_CLKSourceConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[103]"></a>HAL_InitTick</STRONG> (Thumb, 228 bytes, Stack size 64 bytes, stm32h5xx_hal_timebase_tim.o(.text.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetClockConfig
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[104]"></a>HAL_MspInit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32h5xx_hal_msp.o(.text.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[c0]"></a>HAL_NVIC_DisableIRQ</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, stm32h5xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_NVIC_DisableIRQ &rArr; __NVIC_DisableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_DisableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipInit
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_ConfigPC2_AsSPI_MISO
</UL>

<P><STRONG><a name="[be]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, stm32h5xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_NVIC_EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_EXTI2_Configuration
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_EXTI_Enable
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipHalSpiTxRxCpltCallback
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipStart
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_ConfigPC2_AsEXTI2
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[c2]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 46 bytes, Stack size 32 bytes, stm32h5xx_hal_cortex.o(.text.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_EncodePriority
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_GetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_EXTI2_Configuration
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
</UL>

<P><STRONG><a name="[100]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, stm32h5xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_NVIC_SetPriorityGrouping &rArr; __NVIC_SetPriorityGrouping
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[110]"></a>HAL_RCCEx_GetPLL1ClockFreq</STRONG> (Thumb, 748 bytes, Stack size 36 bytes, stm32h5xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPLL1ClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_RCCEx_GetPLL1ClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPeriphCLKFreq
</UL>

<P><STRONG><a name="[111]"></a>HAL_RCCEx_GetPLL2ClockFreq</STRONG> (Thumb, 748 bytes, Stack size 36 bytes, stm32h5xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPLL2ClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_RCCEx_GetPLL2ClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPeriphCLKFreq
</UL>

<P><STRONG><a name="[112]"></a>HAL_RCCEx_GetPLL3ClockFreq</STRONG> (Thumb, 748 bytes, Stack size 36 bytes, stm32h5xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPLL3ClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_RCCEx_GetPLL3ClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPeriphCLKFreq
</UL>

<P><STRONG><a name="[10f]"></a>HAL_RCCEx_GetPeriphCLKFreq</STRONG> (Thumb, 10488 bytes, Stack size 120 bytes, stm32h5xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 172<LI>Call Chain = HAL_RCCEx_GetPeriphCLKFreq &rArr; HAL_RCC_GetPCLK1Freq &rArr; HAL_RCC_GetHCLKFreq &rArr; HAL_RCC_GetSysClockFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPLL3ClockFreq
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPLL2ClockFreq
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPLL1ClockFreq
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK3Freq
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[116]"></a>HAL_RCCEx_PeriphCLKConfig</STRONG> (Thumb, 6382 bytes, Stack size 184 bytes, stm32h5xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCEx_PLL3_Config
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCEx_PLL2_Config
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>

<P><STRONG><a name="[119]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 1172 bytes, Stack size 32 bytes, stm32h5xx_hal_rcc.o(.text.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[105]"></a>HAL_RCC_GetClockConfig</STRONG> (Thumb, 108 bytes, Stack size 12 bytes, stm32h5xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_RCC_GetClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[115]"></a>HAL_RCC_GetHCLKFreq</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, stm32h5xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = HAL_RCC_GetHCLKFreq &rArr; HAL_RCC_GetSysClockFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPeriphCLKFreq
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK3Freq
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
</UL>

<P><STRONG><a name="[106]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32h5xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_RCC_GetPCLK1Freq &rArr; HAL_RCC_GetHCLKFreq &rArr; HAL_RCC_GetSysClockFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPeriphCLKFreq
</UL>

<P><STRONG><a name="[113]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32h5xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_RCC_GetPCLK2Freq &rArr; HAL_RCC_GetHCLKFreq &rArr; HAL_RCC_GetSysClockFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPeriphCLKFreq
</UL>

<P><STRONG><a name="[114]"></a>HAL_RCC_GetPCLK3Freq</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32h5xx_hal_rcc.o(.text.HAL_RCC_GetPCLK3Freq))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_RCC_GetPCLK3Freq &rArr; HAL_RCC_GetHCLKFreq &rArr; HAL_RCC_GetSysClockFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPeriphCLKFreq
</UL>

<P><STRONG><a name="[101]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 700 bytes, Stack size 36 bytes, stm32h5xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_RCC_GetSysClockFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPeriphCLKFreq
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
</UL>

<P><STRONG><a name="[11a]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 2556 bytes, Stack size 40 bytes, stm32h5xx_hal_rcc.o(.text.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = HAL_RCC_OscConfig &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetHCLKFreq
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[11b]"></a>HAL_SPI_Abort</STRONG> (Thumb, 536 bytes, Stack size 24 bytes, stm32h5xx_hal_spi.o(.text.HAL_SPI_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_SPI_Abort &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_GetError
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_AbortTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_read
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_write
</UL>

<P><STRONG><a name="[df]"></a>HAL_SPI_DMAStop</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, stm32h5xx_hal_spi.o(.text.HAL_SPI_DMAStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_SPI_DMAStop
</UL>
<BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipHalSpiTxRxCpltCallback
</UL>

<P><STRONG><a name="[121]"></a>HAL_SPI_ErrorCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_spi.o(.text.HAL_SPI_ErrorCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_SPI_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_DMAAbortOnError
</UL>

<P><STRONG><a name="[17d]"></a>HAL_SPI_GetState</STRONG> (Thumb, 14 bytes, Stack size 4 bytes, stm32h5xx_hal_spi.o(.text.HAL_SPI_GetState))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_SPI_GetState
</UL>
<BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_read
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_write
</UL>

<P><STRONG><a name="[11e]"></a>HAL_SPI_IRQHandler</STRONG> (Thumb, 924 bytes, Stack size 40 bytes, stm32h5xx_hal_spi.o(.text.HAL_SPI_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 156<LI>Call Chain = HAL_SPI_IRQHandler &rArr; HAL_SPI_TxRxCpltCallback &rArr; AdcChipHalSpiTxRxCpltCallback &rArr; AD4170EndIrq &rArr; ADC0_ConfigPC2_AsEXTI2 &rArr; HAL_NVIC_EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxRxCpltCallback
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_SuspendCallback
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_RxCpltCallback
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TxCpltCallback
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI3_IRQHandler
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI2_IRQHandler
</UL>

<P><STRONG><a name="[126]"></a>HAL_SPI_Init</STRONG> (Thumb, 904 bytes, Stack size 32 bytes, stm32h5xx_hal_spi.o(.text.HAL_SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 672 + Unknown Stack Size
<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_GetPacketSize
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI3_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
</UL>

<P><STRONG><a name="[128]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 1254 bytes, Stack size 432 bytes, spi.o(.text.HAL_SPI_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 640 + Unknown Stack Size
<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr8
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[123]"></a>HAL_SPI_RxCpltCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_SPI_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
</UL>

<P><STRONG><a name="[11f]"></a>HAL_SPI_SuspendCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_spi.o(.text.HAL_SPI_SuspendCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_SPI_SuspendCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
</UL>

<P><STRONG><a name="[12b]"></a>HAL_SPI_TransmitReceive</STRONG> (Thumb, 2072 bytes, Stack size 72 bytes, stm32h5xx_hal_spi.o(.text.HAL_SPI_TransmitReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_SPI_TransmitReceive &rArr; SPI_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitOnFlagUntilTimeout
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_CloseTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_read
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_write
</UL>

<P><STRONG><a name="[124]"></a>HAL_SPI_TxCpltCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_SPI_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
</UL>

<P><STRONG><a name="[122]"></a>HAL_SPI_TxRxCpltCallback</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, usrappmain.o(.text.HAL_SPI_TxRxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = HAL_SPI_TxRxCpltCallback &rArr; AdcChipHalSpiTxRxCpltCallback &rArr; AD4170EndIrq &rArr; ADC0_ConfigPC2_AsEXTI2 &rArr; HAL_NVIC_EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipHalSpiTxRxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
</UL>

<P><STRONG><a name="[102]"></a>HAL_SYSTICK_CLKSourceConfig</STRONG> (Thumb, 154 bytes, Stack size 8 bytes, stm32h5xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_CLKSourceConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[13a]"></a>HAL_TIMEx_Break2Callback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_tim_ex.o(.text.HAL_TIMEx_Break2Callback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_Break2Callback
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[139]"></a>HAL_TIMEx_BreakCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_BreakCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[13c]"></a>HAL_TIMEx_CommutCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_CommutCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[13e]"></a>HAL_TIMEx_DirectionChangeCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_tim_ex.o(.text.HAL_TIMEx_DirectionChangeCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_DirectionChangeCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[13d]"></a>HAL_TIMEx_EncoderIndexCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_tim_ex.o(.text.HAL_TIMEx_EncoderIndexCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_EncoderIndexCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[13f]"></a>HAL_TIMEx_IndexErrorCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_tim_ex.o(.text.HAL_TIMEx_IndexErrorCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_IndexErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[15e]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 530 bytes, Stack size 20 bytes, stm32h5xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[140]"></a>HAL_TIMEx_TransitionErrorCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_tim_ex.o(.text.HAL_TIMEx_TransitionErrorCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIMEx_TransitionErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[107]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 168 bytes, Stack size 16 bytes, stm32h5xx_hal_tim.o(.text.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[12d]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 258 bytes, Stack size 40 bytes, tim.o(.text.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[108]"></a>HAL_TIM_Base_Start_IT</STRONG> (Thumb, 402 bytes, Stack size 12 bytes, stm32h5xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_TIM_Base_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[12f]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 520 bytes, Stack size 32 bytes, stm32h5xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = HAL_TIM_ConfigClockSource &rArr; TIM_ETR_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETR_SetConfig
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_ConfigInputStage
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRx_SetConfig
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[135]"></a>HAL_TIM_IC_CaptureCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_IC_CaptureCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[134]"></a>HAL_TIM_IRQHandler</STRONG> (Thumb, 768 bytes, Stack size 24 bytes, stm32h5xx_hal_tim.o(.text.HAL_TIM_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback &rArr; ECAT_CheckTimer
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PeriodElapsedCallback
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_TransitionErrorCallback
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_IndexErrorCallback
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_DirectionChangeCallback
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_EncoderIndexCallback
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_CommutCallback
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_TriggerCallback
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_Break2Callback
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_BreakCallback
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_OC_DelayElapsedCallback
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IC_CaptureCallback
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_PWM_PulseFinishedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM7_IRQHandler
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_IRQHandler
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_IRQHandler
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_IRQHandler
</UL>

<P><STRONG><a name="[136]"></a>HAL_TIM_OC_DelayElapsedCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_OC_DelayElapsedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[137]"></a>HAL_TIM_PWM_PulseFinishedCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_PWM_PulseFinishedCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[138]"></a>HAL_TIM_PeriodElapsedCallback</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, main.o(.text.HAL_TIM_PeriodElapsedCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_PeriodElapsedCallback &rArr; ECAT_CheckTimer
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ECAT_CheckTimer
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[13b]"></a>HAL_TIM_TriggerCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_tim.o(.text.HAL_TIM_TriggerCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_TIM_TriggerCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>

<P><STRONG><a name="[160]"></a>HAL_UARTEx_DisableFifoMode</STRONG> (Thumb, 124 bytes, Stack size 12 bytes, stm32h5xx_hal_uart_ex.o(.text.HAL_UARTEx_DisableFifoMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = HAL_UARTEx_DisableFifoMode
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[eb]"></a>HAL_UARTEx_ReceiveToIdle_DMA</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, stm32h5xx_hal_uart_ex.o(.text.HAL_UARTEx_ReceiveToIdle_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommuAdpt_Init
</UL>

<P><STRONG><a name="[143]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 354 bytes, Stack size 24 bytes, commuadpt.o(.text.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_AbortReceive
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommuAdpt_Exe
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[14f]"></a>HAL_UARTEx_RxFifoFullCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_uart_ex.o(.text.HAL_UARTEx_RxFifoFullCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UARTEx_RxFifoFullCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[146]"></a>HAL_UARTEx_SetRxFifoThreshold</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, stm32h5xx_hal_uart_ex.o(.text.HAL_UARTEx_SetRxFifoThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_UARTEx_SetRxFifoThreshold &rArr; UARTEx_SetNbDataToProcess
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTEx_SetNbDataToProcess
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[148]"></a>HAL_UARTEx_SetTxFifoThreshold</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, stm32h5xx_hal_uart_ex.o(.text.HAL_UARTEx_SetTxFifoThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_UARTEx_SetTxFifoThreshold &rArr; UARTEx_SetNbDataToProcess
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UARTEx_SetNbDataToProcess
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[14e]"></a>HAL_UARTEx_TxFifoEmptyCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_uart_ex.o(.text.HAL_UARTEx_TxFifoEmptyCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UARTEx_TxFifoEmptyCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[14c]"></a>HAL_UARTEx_WakeupCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_uart_ex.o(.text.HAL_UARTEx_WakeupCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UARTEx_WakeupCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[144]"></a>HAL_UART_AbortReceive</STRONG> (Thumb, 278 bytes, Stack size 32 bytes, stm32h5xx_hal_uart.o(.text.HAL_UART_AbortReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_UART_AbortReceive &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_GetError
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[149]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, commuadpt.o(.text.HAL_UART_ErrorCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = HAL_UART_ErrorCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[14a]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 1346 bytes, Stack size 64 bytes, stm32h5xx_hal_uart.o(.text.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = HAL_UART_IRQHandler &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxFifoFullCallback
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_TxFifoEmptyCallback
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_WakeupCallback
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[150]"></a>HAL_UART_Init</STRONG> (Thumb, 170 bytes, Stack size 16 bytes, stm32h5xx_hal_uart.o(.text.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 568 + Unknown Stack Size
<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_AdvFeatureConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[151]"></a>HAL_UART_MspInit</STRONG> (Thumb, 476 bytes, Stack size 344 bytes, usart.o(.text.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 552 + Unknown Stack Size
<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr8
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[174]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_uart.o(.text.HAL_UART_RxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UART_RxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[175]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UART_RxHalfCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[155]"></a>HAL_UART_Transmit</STRONG> (Thumb, 340 bytes, Stack size 48 bytes, stm32h5xx_hal_uart.o(.text.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendData
</UL>

<P><STRONG><a name="[ed]"></a>HAL_UART_Transmit_DMA</STRONG> (Thumb, 420 bytes, Stack size 32 bytes, stm32h5xx_hal_uart.o(.text.HAL_UART_Transmit_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_UART_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMAEx_List_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommuAdpt_StaticWrite
</UL>

<P><STRONG><a name="[176]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, commuadpt.o(.text.HAL_UART_TxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTransmit_IT
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMATransmitCplt
</UL>

<P><STRONG><a name="[177]"></a>HAL_UART_TxHalfCpltCallback</STRONG> (Thumb, 8 bytes, Stack size 4 bytes, stm32h5xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = HAL_UART_TxHalfCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMATxHalfCplt
</UL>

<P><STRONG><a name="[185]"></a>HW_Release</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, el9800hw.o(.text.HW_Release))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32h5xx_it.o(.text.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[157]"></a>MBCommuAdpt_Init</STRONG> (Thumb, 172 bytes, Stack size 40 bytes, mbcommuadpt.o(.text.MBCommuAdpt_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = MBCommuAdpt_Init &rArr; ModbusRtuSlave_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusRtuSlave_RegisterCommRecver
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusRtuSlave_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrAppMain_InitHw
</UL>

<P><STRONG><a name="[16b]"></a>MBCommuAdpt_SimpPreChk</STRONG> (Thumb, 128 bytes, Stack size 12 bytes, mbcommuadpt.o(.text.MBCommuAdpt_SimpPreChk))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = MBCommuAdpt_SimpPreChk
</UL>
<BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SynReply_SimpPreChk
</UL>

<P><STRONG><a name="[167]"></a>MBCommuAdpt_UartRecvedFn</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, mbcommuadpt.o(.text.MBCommuAdpt_UartRecvedFn))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = MBCommuAdpt_UartRecvedFn
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SynReply_OnRx
</UL>

<P><STRONG><a name="[15a]"></a>MX_GPIO_Init</STRONG> (Thumb, 232 bytes, Stack size 64 bytes, gpio.o(.text.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15b]"></a>MX_SPI2_Init</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, spi.o(.text.MX_SPI2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 688 + Unknown Stack Size
<LI>Call Chain = MX_SPI2_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15c]"></a>MX_SPI3_Init</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, spi.o(.text.MX_SPI3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 688 + Unknown Stack Size
<LI>Call Chain = MX_SPI3_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15d]"></a>MX_TIM3_Init</STRONG> (Thumb, 136 bytes, Stack size 40 bytes, tim.o(.text.MX_TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = MX_TIM3_Init &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[15f]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, usart.o(.text.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 584 + Unknown Stack Size
<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_DisableFifoMode
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetRxFifoThreshold
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetTxFifoThreshold
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ee]"></a>Matrix_Add</STRONG> (Thumb, 114 bytes, Stack size 24 bytes, usrappmain.o(.text.Matrix_Add))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Matrix_Add
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DecplNN_Calc
</UL>

<P><STRONG><a name="[d7]"></a>Matrix_Multiply</STRONG> (Thumb, 198 bytes, Stack size 28 bytes, usrappmain.o(.text.Matrix_Multiply))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Matrix_Multiply
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DecplNN_Calc
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_OnAllLgChlsCod16
</UL>

<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32h5xx_it.o(.text.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[92]"></a>ModbusRtuCalCrc16</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, mbcommuadpt.o(.text.ModbusRtuCalCrc16))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = ModbusRtuCalCrc16 &rArr; ModbusRtuCalCrc16Seg
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusRtuCalCrc16Seg
</UL>
<BR>[Address Reference Count : 2]<UL><LI> mbcommuadpt.o(.text.ModbusRtuSlave_Init)
<LI> mbcommuadpt.o(.text.MBCommuAdpt_Init)
</UL>
<P><STRONG><a name="[161]"></a>ModbusRtuCalCrc16Seg</STRONG> (Thumb, 120 bytes, Stack size 20 bytes, mbcommuadpt.o(.text.ModbusRtuCalCrc16Seg))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = ModbusRtuCalCrc16Seg
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusRtuCalCrc16
</UL>

<P><STRONG><a name="[9a]"></a>ModbusRtuSlave_HandleRecvedPack</STRONG> (Thumb, 798 bytes, Stack size 56 bytes, mbcommuadpt.o(.text.ModbusRtuSlave_HandleRecvedPack))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = ModbusRtuSlave_HandleRecvedPack &rArr; AckReadAIs &rArr; MbReadOdVals
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckReadAIs
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckWriteHoldMulReg
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckWriteHoldReg
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckReadHoldReg
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mbcommuadpt.o(.text.ModbusRtuSlave_RegisterCommRecver)
</UL>
<P><STRONG><a name="[158]"></a>ModbusRtuSlave_Init</STRONG> (Thumb, 166 bytes, Stack size 28 bytes, mbcommuadpt.o(.text.ModbusRtuSlave_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = ModbusRtuSlave_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MBCommuAdpt_Init
</UL>

<P><STRONG><a name="[159]"></a>ModbusRtuSlave_RegisterCommRecver</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, mbcommuadpt.o(.text.ModbusRtuSlave_RegisterCommRecver))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ModbusRtuSlave_RegisterCommRecver
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MBCommuAdpt_Init
</UL>

<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32h5xx_it.o(.text.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>PendSV_Handler</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, portasm.o(.text.PendSV_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = PendSV_Handler &rArr; vTaskSwitchContext
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>SPI2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32h5xx_it.o(.text.SPI2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = SPI2_IRQHandler &rArr; HAL_SPI_IRQHandler &rArr; HAL_SPI_TxRxCpltCallback &rArr; AdcChipHalSpiTxRxCpltCallback &rArr; AD4170EndIrq &rArr; ADC0_ConfigPC2_AsEXTI2 &rArr; HAL_NVIC_EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>SPI3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32h5xx_it.o(.text.SPI3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = SPI3_IRQHandler &rArr; HAL_SPI_IRQHandler &rArr; HAL_SPI_TxRxCpltCallback &rArr; AdcChipHalSpiTxRxCpltCallback &rArr; AD4170EndIrq &rArr; ADC0_ConfigPC2_AsEXTI2 &rArr; HAL_NVIC_EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>SVC_Handler</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, portasm.o(.text.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[163]"></a>SynReply_Init</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, synreplyv2.o(.text.SynReply_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = SynReply_Init &rArr; CommuAdpt_Init &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommuAdpt_Init
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommuAdpt_RegTxedClb
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommuAdpt_RegRxPreFiltClb
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommuAdpt_RegRxClb
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrAppMain_InitHw
</UL>

<P><STRONG><a name="[9c]"></a>SynReply_OnRx</STRONG> (Thumb, 1000 bytes, Stack size 32 bytes, synreplyv2.o(.text.SynReply_OnRx))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = SynReply_OnRx &rArr; cmWrite &rArr; CommuAdpt_StaticWrite &rArr; HAL_UART_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmWrite
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmRead
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ChkSum
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MBCommuAdpt_UartRecvedFn
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_SetNewBuf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> synreplyv2.o(.text.SynReply_Init)
</UL>
<P><STRONG><a name="[9d]"></a>SynReply_SimpPreChk</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, synreplyv2.o(.text.SynReply_SimpPreChk))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = SynReply_SimpPreChk &rArr; MBCommuAdpt_SimpPreChk
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MBCommuAdpt_SimpPreChk
</UL>
<BR>[Address Reference Count : 1]<UL><LI> synreplyv2.o(.text.SynReply_Init)
</UL>
<P><STRONG><a name="[a]"></a>SysTick_Handler</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, cmsis_os2.o(.text.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SysTick_Handler &rArr; xPortSysTickHandler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskGetSchedulerState
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[16e]"></a>SystemClock_Config</STRONG> (Thumb, 214 bytes, Stack size 384 bytes, main.o(.text.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 608 + Unknown Stack Size
<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_OscConfig &rArr; HAL_InitTick &rArr; HAL_TIM_Base_Init &rArr; HAL_TIM_Base_MspInit &rArr; HAL_NVIC_SetPriority &rArr; NVIC_EncodePriority
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr8
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8a]"></a>SystemInit</STRONG> (Thumb, 310 bytes, Stack size 8 bytes, system_stm32h5xx.o(.text.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SystemInit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(.text)
</UL>
<P><STRONG><a name="[37]"></a>TIM2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, el9800hw.o(.text.TIM2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = TIM2_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback &rArr; ECAT_CheckTimer
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM3_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32h5xx_it.o(.text.TIM3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = TIM3_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback &rArr; ECAT_CheckTimer
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM4_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32h5xx_it.o(.text.TIM4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = TIM4_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback &rArr; ECAT_CheckTimer
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM7_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32h5xx_it.o(.text.TIM7_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = TIM7_IRQHandler &rArr; HAL_TIM_IRQHandler &rArr; HAL_TIM_PeriodElapsedCallback &rArr; ECAT_CheckTimer
</UL>
<BR>[Calls]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[12e]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 858 bytes, Stack size 12 bytes, stm32h5xx_hal_tim.o(.text.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[130]"></a>TIM_ETR_SetConfig</STRONG> (Thumb, 52 bytes, Stack size 20 bytes, stm32h5xx_hal_tim.o(.text.TIM_ETR_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[ef]"></a>TanSigMatf32</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, decplbyneuralnet.o(.text.TanSigMatf32))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = TanSigMatf32 &rArr; __hardfp_exp &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DecplNN_Calc
</UL>

<P><STRONG><a name="[162]"></a>ToTrigSaveAppCfg</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, usrappmain.o(.text.ToTrigSaveAppCfg))
<BR><BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cmWrite
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OnWriteHoldRegs
</UL>

<P><STRONG><a name="[152]"></a>UART_AdvFeatureConfig</STRONG> (Thumb, 328 bytes, Stack size 4 bytes, stm32h5xx_hal_uart.o(.text.UART_AdvFeatureConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = UART_AdvFeatureConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[154]"></a>UART_CheckIdleState</STRONG> (Thumb, 326 bytes, Stack size 40 bytes, stm32h5xx_hal_uart.o(.text.UART_CheckIdleState))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = UART_CheckIdleState &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[153]"></a>UART_SetConfig</STRONG> (Thumb, 1048 bytes, Stack size 48 bytes, stm32h5xx_hal_uart.o(.text.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 220<LI>Call Chain = UART_SetConfig &rArr; HAL_RCCEx_GetPeriphCLKFreq &rArr; HAL_RCC_GetPCLK1Freq &rArr; HAL_RCC_GetHCLKFreq &rArr; HAL_RCC_GetSysClockFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_GetPeriphCLKFreq
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[142]"></a>UART_Start_Receive_DMA</STRONG> (Thumb, 472 bytes, Stack size 40 bytes, stm32h5xx_hal_uart.o(.text.UART_Start_Receive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMAEx_List_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>

<P><STRONG><a name="[156]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 268 bytes, Stack size 32 bytes, stm32h5xx_hal_uart.o(.text.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_CheckIdleState
</UL>

<P><STRONG><a name="[44]"></a>USART1_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32h5xx_it.o(.text.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[93]"></a>UartWrite</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, mbcommuadpt.o(.text.UartWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = UartWrite &rArr; CommuAdpt_StaticWrite &rArr; HAL_UART_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommuAdpt_StaticWrite
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mbcommuadpt.o(.text.MBCommuAdpt_Init)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32h5xx_it.o(.text.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32h563xx.o(RESET)
</UL>
<P><STRONG><a name="[da]"></a>Usart_SendData</STRONG> (Thumb, 144 bytes, Stack size 24 bytes, usart.o(.text.Usart_SendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = Usart_SendData &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_StartAdc0InterruptTest
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_SendAdc0DataVia485
</UL>

<P><STRONG><a name="[179]"></a>UsrAppMain_InitHw</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usrappmain.o(.text.UsrAppMain_InitHw))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = UsrAppMain_InitHw &rArr; AdcAdpt_InitAdcs &rArr; AdcChipInit &rArr; ad4170_read_register16 &rArr; ad4170_spi_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SynReply_Init
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MBCommuAdpt_Init
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AppCfg_Init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_InitAdcs
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d9]"></a>ValDivision_GetDivedFloat</STRONG> (Thumb, 304 bytes, Stack size 32 bytes, usrappmain.o(.text.ValDivision_GetDivedFloat))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = ValDivision_GetDivedFloat &rArr; f32mod &rArr; __aeabi_f2d
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f32mod
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmpgt
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_OnAllLgChlsCod16
</UL>

<P><STRONG><a name="[17b]"></a>f32mod</STRONG> (Thumb, 96 bytes, Stack size 24 bytes, usrappmain.o(.text.f32mod))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = f32mod &rArr; __aeabi_f2d
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dcmpge
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ValDivision_GetDivedFloat
</UL>

<P><STRONG><a name="[ac]"></a>main</STRONG> (Thumb, 142 bytes, Stack size 40 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 728 + Unknown Stack Size
<LI>Call Chain = main &rArr; MX_SPI3_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_RCCEx_PeriphCLKConfig &rArr; RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HW_Release
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI3_Init
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsrAppMain_InitHw
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_StartAdc0InterruptTest
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_Adc0TestProcess
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[187]"></a>ulSetInterruptMask</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, portasm.o(.text.ulSetInterruptMask))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortSVCHandler_C
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vTaskSwitchContext
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>

<P><STRONG><a name="[189]"></a>vClearInterruptMask</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, portasm.o(.text.vClearInterruptMask))
<BR><BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>

<P><STRONG><a name="[9b]"></a>vPortSVCHandler_C</STRONG> (Thumb, 50 bytes, Stack size 24 bytes, port.o(.text.vPortSVCHandler_C))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vPortSVCHandler_C
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulSetInterruptMask
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vRestoreContextOfFirstTask
</UL>
<BR>[Address Reference Count : 1]<UL><LI> portasm.o(.text.SVC_Handler)
</UL>
<P><STRONG><a name="[186]"></a>vRestoreContextOfFirstTask</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, portasm.o(.text.vRestoreContextOfFirstTask))
<BR><BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vPortSVCHandler_C
</UL>

<P><STRONG><a name="[a1]"></a>vTaskSwitchContext</STRONG> (Thumb, 192 bytes, Stack size 16 bytes, tasks.o(.text.vTaskSwitchContext))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = vTaskSwitchContext
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulSetInterruptMask
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>

<P><STRONG><a name="[16d]"></a>xPortSysTickHandler</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, port.o(.text.xPortSysTickHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = xPortSysTickHandler &rArr; xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vClearInterruptMask
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulSetInterruptMask
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[16c]"></a>xTaskGetSchedulerState</STRONG> (Thumb, 56 bytes, Stack size 4 bytes, tasks.o(.text.xTaskGetSchedulerState))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = xTaskGetSchedulerState
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[188]"></a>xTaskIncrementTick</STRONG> (Thumb, 630 bytes, Stack size 40 bytes, tasks.o(.text.xTaskIncrementTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = xTaskIncrementTick
</UL>
<BR>[Calls]<UL><LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prvResetNextTaskUnblockTime
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ulSetInterruptMask
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xPortSysTickHandler
</UL>

<P><STRONG><a name="[198]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[171]"></a>__hardfp_exp</STRONG> (Thumb, 714 bytes, Stack size 72 bytes, exp.o(i.__hardfp_exp))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __hardfp_exp &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TanSigMatf32
</UL>

<P><STRONG><a name="[196]"></a>__kernel_poly</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[18b]"></a>__mathlib_dbl_infnan</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __mathlib_dbl_infnan &rArr; __aeabi_dadd
</UL>
<BR>[Calls]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[18d]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_overflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_overflow &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[18f]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_underflow &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[172]"></a>__aeabi_d2f</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TanSigMatf32
</UL>

<P><STRONG><a name="[199]"></a>_d2f</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[192]"></a>__aeabi_dadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[19c]"></a>_dadd</STRONG> (Thumb, 332 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
</UL>

<P><STRONG><a name="[1a1]"></a>__fpl_dcmp_Inf</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, dcmpi.o(x$fpl$dcmpinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpge
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpeq
</UL>

<P><STRONG><a name="[197]"></a>__aeabi_ddiv</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[19f]"></a>_ddiv</STRONG> (Thumb, 560 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[1ea]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, deqf.o(x$fpl$deqf), UNUSED)

<P><STRONG><a name="[1a0]"></a>_dcmpeq</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, deqf.o(x$fpl$deqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dneq
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_deq
</UL>

<P><STRONG><a name="[193]"></a>__aeabi_d2iz</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dfix.o(x$fpl$dfix))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[1a2]"></a>_dfix</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, dfix.o(x$fpl$dfix), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[194]"></a>__aeabi_i2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dflt))
<BR><BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[1eb]"></a>_dflt</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dflt), UNUSED)

<P><STRONG><a name="[1ec]"></a>__aeabi_cdcmpge</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dgeqf.o(x$fpl$dgeqf), UNUSED)

<P><STRONG><a name="[1a3]"></a>_dcmpge</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, dgeqf.o(x$fpl$dgeqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dgeq
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dgr
</UL>

<P><STRONG><a name="[18e]"></a>__aeabi_cdcmple</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[1a4]"></a>_dcmple</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dls
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dleq
</UL>

<P><STRONG><a name="[1a7]"></a>__fpl_dcmple_InfNaN</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drcmple
</UL>

<P><STRONG><a name="[191]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[1a5]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[19b]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpge
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dfix
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpeq
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[19e]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
</UL>

<P><STRONG><a name="[18c]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, drleqf.o(x$fpl$drleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[1a6]"></a>_drcmple</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, drleqf.o(x$fpl$drleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmple_InfNaN
</UL>

<P><STRONG><a name="[195]"></a>__aeabi_drsub</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[1a8]"></a>_drsb</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[190]"></a>__aeabi_dsub</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_exp
</UL>

<P><STRONG><a name="[1aa]"></a>_dsub</STRONG> (Thumb, 472 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[170]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f32mod
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TanSigMatf32
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ValDivision_GetDivedFloat
</UL>

<P><STRONG><a name="[1ab]"></a>_f2d</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
</UL>

<P><STRONG><a name="[1ed]"></a>__aeabi_dcmpeq</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)

<P><STRONG><a name="[1ad]"></a>_deq</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpeq
</UL>

<P><STRONG><a name="[1ae]"></a>_dneq</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpeq
</UL>

<P><STRONG><a name="[17a]"></a>__aeabi_dcmpgt</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_dcmpgt
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ValDivision_GetDivedFloat
</UL>

<P><STRONG><a name="[1af]"></a>_dgr</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpge
</UL>

<P><STRONG><a name="[184]"></a>__aeabi_dcmpge</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_dcmpge
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;f32mod
</UL>

<P><STRONG><a name="[1b0]"></a>_dgeq</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpge
</UL>

<P><STRONG><a name="[1ee]"></a>__aeabi_dcmple</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)

<P><STRONG><a name="[1b1]"></a>_dleq</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
</UL>

<P><STRONG><a name="[1ef]"></a>__aeabi_dcmplt</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)

<P><STRONG><a name="[1b2]"></a>_dls</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, dcmp.o(x$fpl$fcmp), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
</UL>

<P><STRONG><a name="[1ac]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[a6]"></a>_fp_init</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[1f0]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[1f1]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[19a]"></a>__fpl_fretinf</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fretinf.o(x$fpl$fretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[e0]"></a>ad4170_write_register</STRONG> (Thumb, 30 bytes, Stack size 24 bytes, adcadpt.o(.text.ad4170_write_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = ad4170_write_register &rArr; ad4170_spi_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_write
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipInit
</UL>

<P><STRONG><a name="[e1]"></a>ad4170_read_register</STRONG> (Thumb, 26 bytes, Stack size 24 bytes, adcadpt.o(.text.ad4170_read_register))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = ad4170_read_register &rArr; ad4170_spi_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_read
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipInit
</UL>

<P><STRONG><a name="[e2]"></a>ad4170_write_register16</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, adcadpt.o(.text.ad4170_write_register16))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = ad4170_write_register16 &rArr; ad4170_spi_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_write
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipStart
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipInit
</UL>

<P><STRONG><a name="[e3]"></a>ad4170_spi_write</STRONG> (Thumb, 418 bytes, Stack size 80 bytes, adcadpt.o(.text.ad4170_spi_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = ad4170_spi_write &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Abort
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_GetState
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_ConfigPC2_AsEXTI2
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_ConfigPC2_AsSPI_MISO
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipInit
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_write_register16
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_write_register
</UL>

<P><STRONG><a name="[e4]"></a>ad4170_read_register16</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, adcadpt.o(.text.ad4170_read_register16))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = ad4170_read_register16 &rArr; ad4170_spi_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_read
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcChipInit
</UL>

<P><STRONG><a name="[17c]"></a>ad4170_spi_read</STRONG> (Thumb, 392 bytes, Stack size 80 bytes, adcadpt.o(.text.ad4170_spi_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = ad4170_spi_read &rArr; HAL_SPI_TransmitReceive &rArr; SPI_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Abort
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_GetState
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_ConfigPC2_AsEXTI2
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_ConfigPC2_AsSPI_MISO
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_read_register16
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_read_register
</UL>

<P><STRONG><a name="[bf]"></a>ADC0_ConfigPC2_AsSPI_MISO</STRONG> (Thumb, 130 bytes, Stack size 32 bytes, adcadpt.o(.text.ADC0_ConfigPC2_AsSPI_MISO))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = ADC0_ConfigPC2_AsSPI_MISO &rArr; HAL_NVIC_DisableIRQ &rArr; __NVIC_DisableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_DisableIRQ
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_InitAdcs
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_read
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_write
</UL>

<P><STRONG><a name="[bc]"></a>ADC0_ConfigPC2_AsEXTI2</STRONG> (Thumb, 130 bytes, Stack size 32 bytes, adcadpt.o(.text.ADC0_ConfigPC2_AsEXTI2))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = ADC0_ConfigPC2_AsEXTI2 &rArr; HAL_NVIC_EnableIRQ &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AD4170EndIrq
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_read
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ad4170_spi_write
</UL>

<P><STRONG><a name="[d1]"></a>ApplyChipConfigsToHandles</STRONG> (Thumb, 168 bytes, Stack size 12 bytes, adcadpt.o(.text.ApplyChipConfigsToHandles))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ApplyChipConfigsToHandles
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_InitAdcs
</UL>

<P><STRONG><a name="[ce]"></a>Filt</STRONG> (Thumb, 490 bytes, Stack size 32 bytes, adcadpt.o(.text.Filt))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Filt
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_AddSingleChlVal32
</UL>

<P><STRONG><a name="[cd]"></a>AdcAdpt_SendAdc0DataVia485</STRONG> (Thumb, 306 bytes, Stack size 64 bytes, adcadpt.o(.text.AdcAdpt_SendAdc0DataVia485))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = AdcAdpt_SendAdc0DataVia485 &rArr; Usart_SendData &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout &rArr; UART_EndRxTransfer
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_Adc0TestProcess
</UL>

<P><STRONG><a name="[e6]"></a>InitPrmItmArr</STRONG> (Thumb, 262 bytes, Stack size 8 bytes, appcfg.o(.text.InitPrmItmArr))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = InitPrmItmArr
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AppCfg_Init
</UL>

<P><STRONG><a name="[c8]"></a>AckReadHoldReg</STRONG> (Thumb, 290 bytes, Stack size 32 bytes, mbcommuadpt.o(.text.AckReadHoldReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = AckReadHoldReg &rArr; MbReadOdVals
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MbReadOdVals
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckErrCode
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusRtuSlave_HandleRecvedPack
</UL>

<P><STRONG><a name="[cb]"></a>AckWriteHoldReg</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, mbcommuadpt.o(.text.AckWriteHoldReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = AckWriteHoldReg &rArr; MbWriteOdVals
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MbWriteOdVals
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckErrCode
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusRtuSlave_HandleRecvedPack
</UL>

<P><STRONG><a name="[c9]"></a>AckWriteHoldMulReg</STRONG> (Thumb, 240 bytes, Stack size 32 bytes, mbcommuadpt.o(.text.AckWriteHoldMulReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = AckWriteHoldMulReg &rArr; MbWriteOdVals
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MbWriteOdVals
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckErrCode
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusRtuSlave_HandleRecvedPack
</UL>

<P><STRONG><a name="[c5]"></a>AckReadAIs</STRONG> (Thumb, 290 bytes, Stack size 32 bytes, mbcommuadpt.o(.text.AckReadAIs))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = AckReadAIs &rArr; MbReadOdVals
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MbReadOdVals
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckErrCode
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ModbusRtuSlave_HandleRecvedPack
</UL>

<P><STRONG><a name="[c6]"></a>AckErrCode</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, mbcommuadpt.o(.text.AckErrCode))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = AckErrCode
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckReadAIs
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckWriteHoldMulReg
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckWriteHoldReg
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckReadHoldReg
</UL>

<P><STRONG><a name="[c7]"></a>MbReadOdVals</STRONG> (Thumb, 450 bytes, Stack size 44 bytes, mbcommuadpt.o(.text.MbReadOdVals))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = MbReadOdVals
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckReadAIs
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckReadHoldReg
</UL>

<P><STRONG><a name="[ca]"></a>MbWriteOdVals</STRONG> (Thumb, 446 bytes, Stack size 36 bytes, mbcommuadpt.o(.text.MbWriteOdVals))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = MbWriteOdVals
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckWriteHoldMulReg
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AckWriteHoldReg
</UL>

<P><STRONG><a name="[94]"></a>OnSlvReadAIs</STRONG> (Thumb, 132 bytes, Stack size 8 bytes, mbcommuadpt.o(.text.OnSlvReadAIs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OnSlvReadAIs
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mbcommuadpt.o(.text.MBCommuAdpt_Init)
</UL>
<P><STRONG><a name="[95]"></a>OnSlvReadHoldReg</STRONG> (Thumb, 36 bytes, Stack size 4 bytes, mbcommuadpt.o(.text.OnSlvReadHoldReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = OnSlvReadHoldReg
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mbcommuadpt.o(.text.MBCommuAdpt_Init)
</UL>
<P><STRONG><a name="[96]"></a>OnSlvReadDIs</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, mbcommuadpt.o(.text.OnSlvReadDIs))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = OnSlvReadDIs
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mbcommuadpt.o(.text.MBCommuAdpt_Init)
</UL>
<P><STRONG><a name="[97]"></a>OnSlvReadCoils</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, mbcommuadpt.o(.text.OnSlvReadCoils))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OnSlvReadCoils
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mbcommuadpt.o(.text.MBCommuAdpt_Init)
</UL>
<P><STRONG><a name="[98]"></a>OnWriteCoils</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, mbcommuadpt.o(.text.OnWriteCoils))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OnWriteCoils
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mbcommuadpt.o(.text.MBCommuAdpt_Init)
</UL>
<P><STRONG><a name="[99]"></a>OnWriteHoldRegs</STRONG> (Thumb, 120 bytes, Stack size 24 bytes, mbcommuadpt.o(.text.OnWriteHoldRegs))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = OnWriteHoldRegs
</UL>
<BR>[Calls]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ToTrigSaveAppCfg
</UL>
<BR>[Address Reference Count : 1]<UL><LI> mbcommuadpt.o(.text.MBCommuAdpt_Init)
</UL>
<P><STRONG><a name="[e8]"></a>ChkSum</STRONG> (Thumb, 196 bytes, Stack size 24 bytes, synreplyv2.o(.text.ChkSum))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = ChkSum &rArr; HAL_CRC_Calculate &rArr; CRC_Handle_8
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CRC_Calculate
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SynReply_OnRx
</UL>

<P><STRONG><a name="[169]"></a>cmRead</STRONG> (Thumb, 498 bytes, Stack size 24 bytes, synreplyv2.o(.text.cmRead))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = cmRead &rArr; CommuAdpt_StaticWrite &rArr; HAL_UART_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CRC_Calculate
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommuAdpt_StaticWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SynReply_OnRx
</UL>

<P><STRONG><a name="[16a]"></a>cmWrite</STRONG> (Thumb, 1076 bytes, Stack size 136 bytes, synreplyv2.o(.text.cmWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = cmWrite &rArr; CommuAdpt_StaticWrite &rArr; HAL_UART_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CRC_Calculate
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ToTrigSaveAppCfg
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CommuAdpt_StaticWrite
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_RstValZeroOffset
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_AdjValOffsetByCur2ActValDiffs
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_AdjValOffsetByActVals
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_RstCodZeroOffset
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_AdjCodZeroOffsetByCur2ActCodDiffs
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AdcAdpt_AdjCodZeroOffsetByActCods
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SynReply_OnRx
</UL>

<P><STRONG><a name="[9e]"></a>OnTxAdsComplete</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, synreplyv2.o(.text.OnTxAdsComplete))
<BR>[Address Reference Count : 1]<UL><LI> synreplyv2.o(.text.SynReply_Init)
</UL>
<P><STRONG><a name="[131]"></a>TIM_TI1_ConfigInputStage</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, stm32h5xx_hal_tim.o(.text.TIM_TI1_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_TI1_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[132]"></a>TIM_ITRx_SetConfig</STRONG> (Thumb, 48 bytes, Stack size 12 bytes, stm32h5xx_hal_tim.o(.text.TIM_ITRx_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_ITRx_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[133]"></a>TIM_TI2_ConfigInputStage</STRONG> (Thumb, 82 bytes, Stack size 20 bytes, stm32h5xx_hal_tim.o(.text.TIM_TI2_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_TI2_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[10e]"></a>__NVIC_SetPriorityGrouping</STRONG> (Thumb, 60 bytes, Stack size 12 bytes, stm32h5xx_hal_cortex.o(.text.__NVIC_SetPriorityGrouping))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = __NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>

<P><STRONG><a name="[10b]"></a>__NVIC_GetPriorityGrouping</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32h5xx_hal_cortex.o(.text.__NVIC_GetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[10c]"></a>NVIC_EncodePriority</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, stm32h5xx_hal_cortex.o(.text.NVIC_EncodePriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = NVIC_EncodePriority
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[10d]"></a>__NVIC_SetPriority</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, stm32h5xx_hal_cortex.o(.text.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
</UL>

<P><STRONG><a name="[10a]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 48 bytes, Stack size 4 bytes, stm32h5xx_hal_cortex.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>

<P><STRONG><a name="[109]"></a>__NVIC_DisableIRQ</STRONG> (Thumb, 56 bytes, Stack size 4 bytes, stm32h5xx_hal_cortex.o(.text.__NVIC_DisableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_DisableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_DisableIRQ
</UL>

<P><STRONG><a name="[fa]"></a>DMA_Init</STRONG> (Thumb, 1930 bytes, Stack size 20 bytes, stm32h5xx_hal_dma.o(.text.DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DMA_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[fc]"></a>DMA_SetConfig</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, stm32h5xx_hal_dma.o(.text.DMA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[f6]"></a>DMA_List_GetCLLRNodeInfo</STRONG> (Thumb, 94 bytes, Stack size 12 bytes, stm32h5xx_hal_dma_ex.o(.text.DMA_List_GetCLLRNodeInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DMA_List_GetCLLRNodeInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMAEx_List_Start_IT
</UL>

<P><STRONG><a name="[117]"></a>RCCEx_PLL2_Config</STRONG> (Thumb, 364 bytes, Stack size 24 bytes, stm32h5xx_hal_rcc_ex.o(.text.RCCEx_PLL2_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RCCEx_PLL2_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
</UL>

<P><STRONG><a name="[118]"></a>RCCEx_PLL3_Config</STRONG> (Thumb, 364 bytes, Stack size 24 bytes, stm32h5xx_hal_rcc_ex.o(.text.RCCEx_PLL3_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = RCCEx_PLL3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCCEx_PeriphCLKConfig
</UL>

<P><STRONG><a name="[127]"></a>SPI_GetPacketSize</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, stm32h5xx_hal_spi.o(.text.SPI_GetPacketSize))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SPI_GetPacketSize
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[120]"></a>SPI_CloseTransfer</STRONG> (Thumb, 278 bytes, Stack size 8 bytes, stm32h5xx_hal_spi.o(.text.SPI_CloseTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_CloseTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_IRQHandler
</UL>

<P><STRONG><a name="[12c]"></a>SPI_WaitOnFlagUntilTimeout</STRONG> (Thumb, 104 bytes, Stack size 32 bytes, stm32h5xx_hal_spi.o(.text.SPI_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPI_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>

<P><STRONG><a name="[11d]"></a>SPI_AbortTransfer</STRONG> (Thumb, 144 bytes, Stack size 4 bytes, stm32h5xx_hal_spi.o(.text.SPI_AbortTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = SPI_AbortTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Abort
</UL>

<P><STRONG><a name="[8d]"></a>SPI_DMAAbortOnError</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, stm32h5xx_hal_spi.o(.text.SPI_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SPI_DMAAbortOnError &rArr; HAL_SPI_ErrorCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32h5xx_hal_spi.o(.text.HAL_SPI_IRQHandler)
</UL>
<P><STRONG><a name="[14b]"></a>UART_EndRxTransfer</STRONG> (Thumb, 158 bytes, Stack size 16 bytes, stm32h5xx_hal_uart.o(.text.UART_EndRxTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[8f]"></a>UART_DMATransmitCplt</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, stm32h5xx_hal_uart.o(.text.UART_DMATransmitCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = UART_DMATransmitCplt &rArr; HAL_UART_TxCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32h5xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
</UL>
<P><STRONG><a name="[90]"></a>UART_DMATxHalfCplt</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, stm32h5xx_hal_uart.o(.text.UART_DMATxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = UART_DMATxHalfCplt &rArr; HAL_UART_TxHalfCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32h5xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
</UL>
<P><STRONG><a name="[91]"></a>UART_DMAError</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, stm32h5xx_hal_uart.o(.text.UART_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = UART_DMAError &rArr; HAL_UART_ErrorCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Address Reference Count : 2]<UL><LI> stm32h5xx_hal_uart.o(.text.HAL_UART_Transmit_DMA)
<LI> stm32h5xx_hal_uart.o(.text.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[173]"></a>UART_EndTxTransfer</STRONG> (Thumb, 92 bytes, Stack size 12 bytes, stm32h5xx_hal_uart.o(.text.UART_EndTxTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = UART_EndTxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[9f]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 260 bytes, Stack size 32 bytes, stm32h5xx_hal_uart.o(.text.UART_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = UART_DMAReceiveCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32h5xx_hal_uart.o(.text.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[a0]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, stm32h5xx_hal_uart.o(.text.UART_DMARxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = UART_DMARxHalfCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32h5xx_hal_uart.o(.text.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[8e]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, stm32h5xx_hal_uart.o(.text.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = UART_DMAAbortOnError &rArr; HAL_UART_ErrorCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32h5xx_hal_uart.o(.text.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[14d]"></a>UART_EndTransmit_IT</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, stm32h5xx_hal_uart.o(.text.UART_EndTransmit_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_EndTransmit_IT &rArr; HAL_UART_TxCpltCallback
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[147]"></a>UARTEx_SetNbDataToProcess</STRONG> (Thumb, 138 bytes, Stack size 8 bytes, stm32h5xx_hal_uart_ex.o(.text.UARTEx_SetNbDataToProcess))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UARTEx_SetNbDataToProcess
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetRxFifoThreshold
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_SetTxFifoThreshold
</UL>

<P><STRONG><a name="[f3]"></a>CRC_Handle_8</STRONG> (Thumb, 220 bytes, Stack size 24 bytes, stm32h5xx_hal_crc.o(.text.CRC_Handle_8))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = CRC_Handle_8
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CRC_Calculate
</UL>

<P><STRONG><a name="[f4]"></a>CRC_Handle_16</STRONG> (Thumb, 100 bytes, Stack size 20 bytes, stm32h5xx_hal_crc.o(.text.CRC_Handle_16))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = CRC_Handle_16
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_CRC_Calculate
</UL>

<P><STRONG><a name="[18a]"></a>prvResetNextTaskUnblockTime</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, tasks.o(.text.prvResetNextTaskUnblockTime))
<BR><BR>[Called By]<UL><LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;xTaskIncrementTick
</UL>

<P><STRONG><a name="[1a9]"></a>_dadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
</UL>

<P><STRONG><a name="[19d]"></a>_dsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
