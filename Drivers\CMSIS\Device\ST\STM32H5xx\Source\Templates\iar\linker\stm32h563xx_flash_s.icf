/*###ICF### Section handled by ICF editor, don't touch! ****/
/*-Editor annotation file-*/
/* IcfEditorFile="$TOOLKIT_DIR$\config\ide\IcfEditor\cortex_v1_0.xml" */
/*-Specials-*/
define symbol __ICFEDIT_intvec_start__ = 0x0C000000;
/*-Memory Regions-*/
define symbol __ICFEDIT_region_ROM_start__     = 0x0C000000;
define symbol __ICFEDIT_region_ROM_end__       = 0x0C0FDFFF;
define symbol __ICFEDIT_region_ROM_NSC_start__ = 0x0C0FE000;
define symbol __ICFEDIT_region_ROM_NSC_end__   = 0x0C0FFFFF;
define symbol __ICFEDIT_region_RAM_start__     = 0x30000000;
define symbol __ICFEDIT_region_RAM_end__       = 0x3004FFFF;

/*-Sizes-*/
define symbol __ICFEDIT_size_cstack__ = 0x400;
define symbol __ICFEDIT_size_heap__   = 0x200;
/**** End of ICF editor section. ###ICF###*/

define symbol __region_ROM_NS_start__    = 0x08100000;
define symbol __region_ROM_NS_end__      = 0x081FFFFF;

define memory mem with size = 4G;
define region ROM_region      = mem:[from __ICFEDIT_region_ROM_start__     to __ICFEDIT_region_ROM_end__];
define region ROM_NSC_region  = mem:[from __ICFEDIT_region_ROM_NSC_start__ to __ICFEDIT_region_ROM_NSC_end__];
define region RAM_region      = mem:[from __ICFEDIT_region_RAM_start__     to __ICFEDIT_region_RAM_end__];

define exported symbol __VTOR_TABLE_start       = __ICFEDIT_intvec_start__;
define exported symbol __VTOR_TABLE_NS_start    = __region_ROM_NS_start__;

define block CSTACK    with alignment = 8, size = __ICFEDIT_size_cstack__   { };
define block HEAP      with alignment = 8, size = __ICFEDIT_size_heap__     { };

initialize by copy { readwrite };
do not initialize  { section .noinit };

place at address mem:__ICFEDIT_intvec_start__ { readonly section .intvec };

place in ROM_region     { readonly };
place in ROM_NSC_region { section Veneer$$CMSE };
place in RAM_region     { readwrite,
                          block CSTACK, block HEAP };