Dependencies for Project 'LAN9252_SixF_0627', Target 'LAN9252_SixF_0627': (DO NOT MODIFY !)
CompilerVersion: 6230000::V6.23::ARMCLANG
F (startup_stm32h563xx.s)(0x686DE15F)(--target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4 -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../Ethercat/Inc -I ../Ethercat/src -I ../USER

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-Wa,armasm,--pd,"__UVISION_VERSION SETA 542" -Wa,armasm,--pd,"STM32H563xx SETA 1" -Wa,armasm,--pd,"_RTE_ SETA 1"

-o lan9252_sixf_0627/startup_stm32h563xx.o)
F (../Core/Src/app_freertos.c)(0x686DE15C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/app_freertos.o -MMD)
I (..\Core\Inc\app_freertos.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h)(0x685D09FE)
I (..\Core\Inc\FreeRTOSConfig.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacro.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacrocommon.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\task.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\list.h)(0x685D09FE)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Middlewares\Third_Party\CMSIS\RTOS2\Include\cmsis_os2.h)(0x685D09FA)
F (../Core/Src/stm32h5xx_it.c)(0x68956F46)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_it.o -MMD)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_it.h)(0x6891C52B)
I (..\USER\AdcAdpt.h)(0x689C98FD)
I (..\USER\..\RS485\Inc\usart.h)(0x686E1EAD)
F (../Core/Src/stm32h5xx_hal_msp.c)(0x687711A5)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_msp.o -MMD)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Core/Src/stm32h5xx_hal_timebase_tim.c)(0x686DE15C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_timebase_tim.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (..\USER\AdcAdpt.c)(0x689C9E38)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/adcadpt.o -MMD)
I (..\USER\AdcAdpt.h)(0x689C98FD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\USER\..\RS485\Inc\usart.h)(0x686E1EAD)
I (..\USER\AppCfg.h)(0x67EB99F8)
I (..\Core\Inc\main.h)(0x68930318)
I (..\USER\SynReplyV2.h)(0x662B4DDD)
I (..\Core\Inc\usart.h)(0x68941C42)
I (..\Core\Inc\spi.h)(0x689C58C6)
I (..\Core\Inc\gpio.h)(0x689C94F3)
F (..\USER\AppCfg.c)(0x6891B87A)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/appcfg.o -MMD)
I (..\USER\AppCfg.h)(0x67EB99F8)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\USER\FlashPersis_HalG4.h)(0x687F3DC8)
I (..\USER\AdcAdpt.h)(0x689C98FD)
I (..\USER\..\RS485\Inc\usart.h)(0x686E1EAD)
I (..\USER\DecplByNeuralNet.h)(0x66B02166)
F (..\USER\CommuAdpt.c)(0x689995AD)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/commuadpt.o -MMD)
I (..\USER\CommuAdpt.h)(0x6891BD08)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Core\Inc\usart.h)(0x68941C42)
I (..\USER\AppCfg.h)(0x67EB99F8)
F (..\USER\DecplByNeuralNet.c)(0x67E94822)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/decplbyneuralnet.o -MMD)
I (..\USER\DecplByNeuralNet.h)(0x66B02166)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Drivers\CMSIS\DSP\Include\arm_math.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\arm_math_types.h)(0x684FDBCD)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x684FDBCC)
I (..\Drivers\CMSIS\DSP\Include\arm_math_memory.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\none.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\utils.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\basic_math_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\interpolation_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\bayes_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\statistics_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\fast_math_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\matrix_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\complex_math_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\controller_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\support_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\distance_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\svm_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\svm_defines.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\transform_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\filtering_functions.h)(0x684FDBCD)
I (..\Drivers\CMSIS\DSP\Include\dsp\quaternion_math_functions.h)(0x684FDBCD)
F (..\USER\FlashPersis_HalG4.c)(0x687F3DC8)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/flashpersis_halg4.o -MMD)
I (..\USER\FlashPersis_HalG4.h)(0x687F3DC8)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (..\USER\MBCommuAdpt.c)(0x67E94773)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/mbcommuadpt.o -MMD)
I (..\USER\CommuAdpt.h)(0x6891BD08)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\USER\AppCfg.h)(0x67EB99F8)
I (..\USER\AdcAdpt.h)(0x689C98FD)
I (..\USER\..\RS485\Inc\usart.h)(0x686E1EAD)
F (..\USER\RtmVlntPdo.c)(0x67E9304B)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/rtmvlntpdo.o -MMD)
I (..\USER\RtmVlntPdo.h)(0x664C0D33)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\USER\CommuAdpt.h)(0x6891BD08)
I (..\USER\AdcAdpt.h)(0x689C98FD)
I (..\USER\..\RS485\Inc\usart.h)(0x686E1EAD)
I (..\USER\AppCfg.h)(0x67EB99F8)
F (..\USER\SynReplyV2.c)(0x689993DF)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/synreplyv2.o -MMD)
I (..\USER\SynReplyV2.h)(0x662B4DDD)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\USER\CommuAdpt.h)(0x6891BD08)
I (..\USER\AdcAdpt.h)(0x689C98FD)
I (..\USER\..\RS485\Inc\usart.h)(0x686E1EAD)
I (..\USER\AppCfg.h)(0x67EB99F8)
F (..\USER\UsrAppMain.c)(0x689C2F93)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/usrappmain.o -MMD)
I (..\USER\UsrAppMain.h)(0x6891C52B)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\USER\CommuAdpt.h)(0x6891BD08)
I (..\USER\AdcAdpt.h)(0x689C98FD)
I (..\USER\..\RS485\Inc\usart.h)(0x686E1EAD)
I (..\USER\SynReplyV2.h)(0x662B4DDD)
I (..\USER\AppCfg.h)(0x67EB99F8)
I (..\USER\RtmVlntPdo.h)(0x664C0D33)
I (..\Core\Inc\tim.h)(0x68886429)
I (..\USER\CanAdpt.h)(0x67DA8319)
F (..\Core\Src\main.c)(0x689C93D2)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/main.o -MMD)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Core\Inc\crc.h)(0x65E6D0B1)
I (..\Core\Inc\dma.h)(0x65771914)
I (..\Core\Inc\fdcan.h)(0x687F514D)
I (..\Core\Inc\iwdg.h)(0x669F20E0)
I (..\Core\Inc\spi.h)(0x689C58C6)
I (..\Core\Inc\tim.h)(0x68886429)
I (..\Core\Inc\usart.h)(0x68941C42)
I (..\Core\Inc\gpio.h)(0x689C94F3)
I (..\Middlewares\Third_Party\CMSIS\RTOS2\Include\cmsis_os2.h)(0x685D09FA)
I (..\USER\bsp_spiflash.h)(0x68956B78)
I (..\USER\CommuAdpt.h)(0x6891BD08)
I (..\USER\SynReplyV2.h)(0x662B4DDD)
I (..\USER\AdcAdpt.h)(0x689C98FD)
I (..\USER\..\RS485\Inc\usart.h)(0x686E1EAD)
I (..\USER\AppCfg.h)(0x67EB99F8)
I (..\USER\RtmVlntPdo.h)(0x664C0D33)
I (..\USER\UsrAppMain.h)(0x6891C52B)
I (..\USER\el9800appl.h)(0x689C357D)
I (..\Ethercat\Inc\ecatappl.h)(0x55795702)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
I (..\Ethercat\Inc\el9800hw.h)(0x6890A3CB)
I (..\Ethercat\Inc\esc.h)(0x55798928)
I (..\Ethercat\Inc\applInterface.h)(0x55795702)
I (..\Ethercat\Inc\ecatslv.h)(0x57A3FC1E)
I (..\Ethercat\Inc\objdef.h)(0x55795702)
I (..\Ethercat\Inc\sdoserv.h)(0x55795702)
I (..\Ethercat\Inc\ecatcoe.h)(0x55795702)
I (..\Ethercat\Inc\mailbox.h)(0x55795702)
F (..\Core\Src\gpio.c)(0x689C94F3)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/gpio.o -MMD)
I (..\Core\Inc\gpio.h)(0x689C94F3)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (..\Core\Src\crc.c)(0x65E6D0B1)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/crc.o -MMD)
I (..\Core\Inc\crc.h)(0x65E6D0B1)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (..\Core\Src\dma.c)(0x687F59D1)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/dma.o -MMD)
I (..\Core\Inc\dma.h)(0x65771914)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (..\Core\Src\iwdg.c)(0x66A31186)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/iwdg.o -MMD)
I (..\Core\Inc\iwdg.h)(0x669F20E0)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (..\Core\Src\spi.c)(0x689C5705)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/spi.o -MMD)
I (..\Core\Inc\spi.h)(0x689C58C6)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (..\Core\Src\tim.c)(0x6895BBD1)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/tim.o -MMD)
I (..\Core\Inc\tim.h)(0x68886429)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (..\Core\Src\usart.c)(0x68941C42)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/usart.o -MMD)
I (..\Core\Inc\usart.h)(0x68941C42)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (..\USER\bsp_spiflash.c)(0x689989BF)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/bsp_spiflash.o -MMD)
I (..\USER\bsp_spiflash.h)(0x68956B78)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
I (..\Ethercat\Inc\el9800hw.h)(0x6890A3CB)
I (..\Ethercat\Inc\esc.h)(0x55798928)
I (..\Core\Inc\main.h)(0x68930318)
F (..\USER\el9800appl.c)(0x689988A8)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/el9800appl.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Ethercat\Inc\applInterface.h)(0x55795702)
I (..\Ethercat\Inc\esc.h)(0x55798928)
I (..\Ethercat\Inc\ecatslv.h)(0x57A3FC1E)
I (..\Ethercat\Inc\el9800hw.h)(0x6890A3CB)
I (..\Ethercat\Inc\objdef.h)(0x55795702)
I (..\Ethercat\Inc\sdoserv.h)(0x55795702)
I (..\Ethercat\Inc\ecatcoe.h)(0x55795702)
I (..\Ethercat\Inc\mailbox.h)(0x55795702)
I (..\Ethercat\Inc\ecatappl.h)(0x55795702)
I (..\USER\el9800appl.h)(0x689C357D)
F (..\USER\el9800hw.c)(0x689CA23A)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/el9800hw.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
I (..\USER\bsp_spiflash.h)(0x68956B78)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Core\Inc\gpio.h)(0x689C94F3)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Core\Inc\tim.h)(0x68886429)
I (..\Ethercat\Inc\ecatslv.h)(0x57A3FC1E)
I (..\Ethercat\Inc\el9800hw.h)(0x6890A3CB)
I (..\Ethercat\Inc\esc.h)(0x55798928)
I (..\Ethercat\Inc\ecatappl.h)(0x55795702)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_tim.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_tim.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_tim_ex.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_tim_ex.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_cortex.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_cortex.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_i2c.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_i2c.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_i2c_ex.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_i2c_ex.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_dma.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_dma.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_dma_ex.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_dma_ex.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_rcc.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_rcc.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_rcc_ex.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_rcc_ex.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_flash.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_flash.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_flash_ex.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_flash_ex.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_gpio.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_gpio.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_pwr.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_pwr.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_pwr_ex.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_pwr_ex.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_exti.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_exti.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_icache.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_icache.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_spi.c)(0x6881CDA7)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_spi.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_spi_ex.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_spi_ex.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_uart.c)(0x6884324E)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_uart.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Drivers/STM32H5xx_HAL_Driver/Src/stm32h5xx_hal_uart_ex.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_uart_ex.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (..\Drivers\STM32H5xx_HAL_Driver\Src\stm32h5xx_hal_crc.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_crc.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (..\Drivers\STM32H5xx_HAL_Driver\Src\stm32h5xx_hal_iwdg.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_iwdg.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (..\Drivers\STM32H5xx_HAL_Driver\Src\stm32h5xx_hal_crc_ex.c)(0x684FDBCB)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stm32h5xx_hal_crc_ex.o -MMD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Core/Src/system_stm32h5xx.c)(0x684FDBCC)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/system_stm32h5xx.o -MMD)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
F (../Middlewares/Third_Party/FreeRTOS/Source/croutine.c)(0x685D09FE)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/croutine.o -MMD)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h)(0x685D09FE)
I (..\Core\Inc\FreeRTOSConfig.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacro.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacrocommon.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\task.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\list.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\croutine.h)(0x685D09FE)
F (../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c)(0x685D09FE)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/event_groups.o -MMD)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h)(0x685D09FE)
I (..\Core\Inc\FreeRTOSConfig.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacro.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacrocommon.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\task.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\list.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\timers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\event_groups.h)(0x685D09FE)
F (../Middlewares/Third_Party/FreeRTOS/Source/list.c)(0x685D09FE)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/list.o -MMD)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h)(0x685D09FE)
I (..\Core\Inc\FreeRTOSConfig.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacro.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacrocommon.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\list.h)(0x685D09FE)
F (../Middlewares/Third_Party/FreeRTOS/Source/queue.c)(0x685D09FE)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/queue.o -MMD)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h)(0x685D09FE)
I (..\Core\Inc\FreeRTOSConfig.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacro.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacrocommon.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\task.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\list.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\queue.h)(0x685D09FE)
F (../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c)(0x685D09FE)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/stream_buffer.o -MMD)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h)(0x685D09FE)
I (..\Core\Inc\FreeRTOSConfig.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacro.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacrocommon.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\task.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\list.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\stream_buffer.h)(0x685D09FE)
F (../Middlewares/Third_Party/FreeRTOS/Source/tasks.c)(0x685D09FE)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/tasks.o -MMD)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h)(0x685D09FE)
I (..\Core\Inc\FreeRTOSConfig.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacro.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacrocommon.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\task.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\list.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\timers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\stack_macros.h)(0x685D09FE)
F (../Middlewares/Third_Party/FreeRTOS/Source/timers.c)(0x685D09FE)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/timers.o -MMD)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h)(0x685D09FE)
I (..\Core\Inc\FreeRTOSConfig.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacro.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacrocommon.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\task.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\list.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\queue.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\timers.h)(0x685D09FE)
F (../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/port.c)(0x685D09FE)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/port.o -MMD)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h)(0x685D09FE)
I (..\Core\Inc\FreeRTOSConfig.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacro.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacrocommon.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\task.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\list.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_syscall_numbers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portasm.h)(0x685D09FE)
F (../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/portasm.c)(0x685D09FE)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/portasm.o -MMD)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portasm.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h)(0x685D09FE)
I (..\Core\Inc\FreeRTOSConfig.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacro.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacrocommon.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_syscall_numbers.h)(0x685D09FE)
F (../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c)(0x685D09FE)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/cmsis_os2.o -MMD)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h)(0x685D09FE)
I (..\Core\Inc\FreeRTOSConfig.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacro.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacrocommon.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\task.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\list.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\event_groups.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\timers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\semphr.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\queue.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\freertos_mpool.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\freertos_os2.h)(0x685D09FE)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Middlewares\Third_Party\CMSIS\RTOS2\Include\cmsis_os2.h)(0x685D09FA)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x684FDBCC)
F (../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c)(0x685D09FE)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/heap_4.o -MMD)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\FreeRTOS.h)(0x685D09FE)
I (..\Core\Inc\FreeRTOSConfig.h)(0x686DE15C)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\projdefs.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\portable.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\deprecated_definitions.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacro.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\portable\GCC\ARM_CM33_NTZ\non_secure\portmacrocommon.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\mpu_wrappers.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\task.h)(0x685D09FE)
I (..\Middlewares\Third_Party\FreeRTOS\Source\include\list.h)(0x685D09FE)
F (..\Ethercat\src\aoeappl.c)(0x54AF3B4C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/aoeappl.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
F (..\Ethercat\src\coeappl.c)(0x689C3865)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/coeappl.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
I (..\Ethercat\Inc\ecatslv.h)(0x57A3FC1E)
I (..\Ethercat\Inc\el9800hw.h)(0x6890A3CB)
I (..\Ethercat\Inc\esc.h)(0x55798928)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Ethercat\Inc\coeappl.h)(0x55795702)
I (..\Ethercat\Inc\objdef.h)(0x55795702)
I (..\Ethercat\Inc\sdoserv.h)(0x55795702)
I (..\Ethercat\Inc\ecatcoe.h)(0x55795702)
I (..\Ethercat\Inc\mailbox.h)(0x55795702)
I (..\USER\el9800appl.h)(0x689C357D)
I (..\Ethercat\Inc\ecatappl.h)(0x55795702)
F (..\Ethercat\src\ecatappl.c)(0x68995F70)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/ecatappl.o -MMD)
I (..\Ethercat\Inc\ecatslv.h)(0x57A3FC1E)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
I (..\Ethercat\Inc\el9800hw.h)(0x6890A3CB)
I (..\Ethercat\Inc\esc.h)(0x55798928)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Ethercat\Inc\ecatappl.h)(0x55795702)
I (..\Ethercat\Inc\coeappl.h)(0x55795702)
I (..\Ethercat\Inc\objdef.h)(0x55795702)
I (..\Ethercat\Inc\sdoserv.h)(0x55795702)
I (..\Ethercat\Inc\ecatcoe.h)(0x55795702)
I (..\Ethercat\Inc\mailbox.h)(0x55795702)
I (..\Ethercat\Inc\applInterface.h)(0x55795702)
I (..\USER\el9800appl.h)(0x689C357D)
F (..\Ethercat\src\foeappl.c)(0x54AF3B4C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/foeappl.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
F (..\Ethercat\src\bootmode.c)(0x54AF3B4C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/bootmode.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
F (..\Ethercat\src\diag.c)(0x54AF3B4C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/diag.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
F (..\Ethercat\src\ecataoe.c)(0x54AF3B4C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/ecataoe.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
F (..\Ethercat\src\ecatcoe.c)(0x55795702)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/ecatcoe.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
I (..\Ethercat\Inc\sdoserv.h)(0x55795702)
I (..\Ethercat\Inc\ecatcoe.h)(0x55795702)
I (..\Ethercat\Inc\mailbox.h)(0x55795702)
F (..\Ethercat\src\ecateoe.c)(0x54AF3B4C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/ecateoe.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
F (..\Ethercat\src\ecatfoe.c)(0x54AF3B4C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/ecatfoe.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
F (..\Ethercat\src\ecatslv.c)(0x64BB7B8C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/ecatslv.o -MMD)
I (..\Ethercat\Inc\ecatslv.h)(0x57A3FC1E)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
I (..\Ethercat\Inc\el9800hw.h)(0x6890A3CB)
I (..\Ethercat\Inc\esc.h)(0x55798928)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Ethercat\Inc\ecatappl.h)(0x55795702)
I (..\Ethercat\Inc\mailbox.h)(0x55795702)
I (..\Ethercat\Inc\ecatcoe.h)(0x55795702)
I (..\Ethercat\Inc\objdef.h)(0x55795702)
I (..\Ethercat\Inc\sdoserv.h)(0x55795702)
I (..\USER\el9800appl.h)(0x689C357D)
F (..\Ethercat\src\ecatsoe.c)(0x54AF3B4C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/ecatsoe.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
F (..\Ethercat\src\emcy.c)(0x54AF3B4C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/emcy.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
F (..\Ethercat\src\eoeappl.c)(0x54AF3B4C)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/eoeappl.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
F (..\Ethercat\src\mailbox.c)(0x64BB3242)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/mailbox.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
I (..\Ethercat\Inc\ecatslv.h)(0x57A3FC1E)
I (..\Ethercat\Inc\el9800hw.h)(0x6890A3CB)
I (..\Ethercat\Inc\esc.h)(0x55798928)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Ethercat\Inc\mailbox.h)(0x55795702)
I (..\Ethercat\Inc\ecatcoe.h)(0x55795702)
F (..\Ethercat\src\objdef.c)(0x689C38DF)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/objdef.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
I (..\Ethercat\Inc\ecatslv.h)(0x57A3FC1E)
I (..\Ethercat\Inc\el9800hw.h)(0x6890A3CB)
I (..\Ethercat\Inc\esc.h)(0x55798928)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Ethercat\Inc\coeappl.h)(0x55795702)
I (..\Ethercat\Inc\objdef.h)(0x55795702)
I (..\Ethercat\Inc\sdoserv.h)(0x55795702)
I (..\Ethercat\Inc\ecatcoe.h)(0x55795702)
I (..\Ethercat\Inc\mailbox.h)(0x55795702)
F (..\Ethercat\src\sdoserv.c)(0x55795702)(-xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc -I ../Drivers/STM32H5xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32H5xx/Include -I ../Drivers/CMSIS/Include -I ../Middlewares/Third_Party/FreeRTOS/Source/include/ -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM33_NTZ/non_secure/ -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/ -I ../Middlewares/Third_Party/CMSIS/RTOS2/Include/ -I ../RS485/Inc -I ../Ethercat/Inc -I ../Drivers/CMSIS/DSP/Include -I ../USER -I ../ad4170

-I./RTE/_LAN9252_SixF_0627

-IF:/Keil_STM32/ARM/PACK/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32H563xx -D_RTE_ -DUSE_HAL_DRIVER -DSTM32H563xx -DAd4170

-o lan9252_sixf_0627/sdoserv.o -MMD)
I (..\Ethercat\Inc\ecat_def.h)(0x689704C4)
I (..\Ethercat\Inc\ecatslv.h)(0x57A3FC1E)
I (..\Ethercat\Inc\el9800hw.h)(0x6890A3CB)
I (..\Ethercat\Inc\esc.h)(0x55798928)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal.h)(0x684FDBCB)
I (..\Core\Inc\stm32h5xx_hal_conf.h)(0x6881D3CD)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_def.h)(0x684FDBCB)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\stm32h563xx.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Include\core_cm33.h)(0x684FDBCC)
I (..\Drivers\CMSIS\Device\ST\STM32H5xx\Include\system_stm32h5xx.h)(0x684FDBCC)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_rcc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_gpio_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_icache.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_dma_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_cortex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_crc_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_flash_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_i2c_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_iwdg.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_pwr_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_spi_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_tim_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_uart_ex.h)(0x684FDBCB)
I (..\Drivers\STM32H5xx_HAL_Driver\Inc\stm32h5xx_hal_exti.h)(0x684FDBCB)
I (..\Core\Inc\main.h)(0x68930318)
I (..\Ethercat\Inc\objdef.h)(0x55795702)
I (..\Ethercat\Inc\sdoserv.h)(0x55795702)
I (..\Ethercat\Inc\ecatcoe.h)(0x55795702)
I (..\Ethercat\Inc\mailbox.h)(0x55795702)
