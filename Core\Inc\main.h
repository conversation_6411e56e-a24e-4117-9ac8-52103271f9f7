/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32h5xx_hal.h"
#include "stm32h5xx_hal_crc.h"
#include "stm32h5xx_hal_iwdg.h"
#include "stm32h5xx_hal_pwr.h"
#include "stm32h5xx_hal_pwr_ex.h"
#include <string.h>
#include <stdio.h>
#include <stdint.h>
/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */


//485Master
#define DfSynCommMstRol					0
//-------------------------Cust PCL-----------------------
//Modbus RTU
#define DfEnModbusRTU						1	


/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */
#define DfLedOn()	HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7,0)
#define DfLedOff()	HAL_GPIO_WritePin(GPIOB, GPIO_PIN_7,1)
#define DfLedToggle()	HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_7)
/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/

#define OSC_IN_Pin GPIO_PIN_0
#define OSC_IN_GPIO_Port GPIOH
#define OSC_OUT_Pin GPIO_PIN_1
#define OSC_OUT_GPIO_Port GPIOH

#define SPI_nINT_Pin GPIO_PIN_1
#define SPI_nINT_GPIO_Port GPIOA
#define SPI_nINT_EXTI_IRQn EXTI1_IRQn

#define SYN0_LAT0_Pin GPIO_PIN_2
#define SYN0_LAT0_GPIO_Port GPIOA
#define SYN0_LAT0_EXTI_IRQn EXTI2_IRQn
#define SYN1_LAT1_Pin GPIO_PIN_3
#define SYN1_LAT1_GPIO_Port GPIOA
#define SYN1_LAT1_EXTI_IRQn EXTI3_IRQn

#define LSPI_CS_Pin GPIO_PIN_4
#define LSPI_CS_GPIO_Port GPIOA
#define LSPI_SCLK_Pin GPIO_PIN_5
#define LSPI_SCLK_GPIO_Port GPIOA
#define LSPI_MISO_Pin GPIO_PIN_6
#define LSPI_MISO_GPIO_Port GPIOA
#define LSPI_MOSI_Pin GPIO_PIN_7
#define LSPI_MOSI_GPIO_Port GPIOA

#define LINK0_Pin GPIO_PIN_0
#define LINK0_GPIO_Port GPIOB
#define LINK1_Pin GPIO_PIN_1
#define LINK1_GPIO_Port GPIOB

#define EESCL_Pin GPIO_PIN_12
#define EESCL_GPIO_Port GPIOD
#define EESDA_Pin GPIO_PIN_7
#define EESDA_GPIO_Port GPIOB

#define RSTn_Pin GPIO_PIN_9
#define RSTn_GPIO_Port GPIOA

/*ADC1*/
#define Spi2Ad1Eti4_Pin GPIO_PIN_2      //ADC1 MISO �ⲿ�ж��½��ش������ж�����׼�� PC2
#define Spi2Ad1Eti4_GPIO_Port GPIOC
#define Spi2Ad1Eti4_EXTI_IRQn EXTI4_IRQn
#define Spi2_Cs_Pin GPIO_PIN_0		//ADC1 CS PA0
#define Spi2_Cs_GPIO_Port GPIOA
/*ADC2*/
#define Spi3Ad2Eti5_Pin GPIO_PIN_11		//ADC2 MISO �ⲿ�ж��½��ش������ж�����׼�� PC11
#define Spi3Ad2Eti5_GPIO_Port GPIOC
#define Spi3Ad2Eti5_EXTI_IRQn EXTI11_IRQn
#define Spi3_Cs_Pin GPIO_PIN_5
#define Spi3_Cs_GPIO_Port GPIOC		//ADC2 CS PC5



/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
