#include "UsrAppMain.h"
#include "CommuAdpt.h"
#include "AdcAdpt.h"
#include "SynReplyV2.h"
#include "AppCfg.h"
#include "RtmVlntPdo.h"
#include "tim.h"
#include "CanAdpt.h"
#include <stdio.h>
#include <string.h>

void HAL_SPI_TxRxCpltCallback(SPI_HandleTypeDef *hspi)
{
	AdcChipHalSpiTxRxCpltCallback( hspi);
	
}

// 娉ㄩ噴锛欸PIO澶栭儴涓柇鍥炶皟宸茬Щ鑷砮l9800hw.c涓粺涓€澶勭悊
// 閬垮厤澶氫釜HAL_GPIO_EXTI_Callback鍑芥暟瀹氫箟鍐茬獊

//-----------------------------------------------------

//void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim){
//	if(htim == &htim6){		
//	}
//}

//-----------------------------------------------------


//LED状态指示锟斤拷
//static void UpAppSt(void)
//{
//	static uint32_t nexChkTick=0;
//	static uint8_t adcOk=1, comOk=1, trigStp=0;
//	//uint32_t curTick=0;
//	
//	if(trigStp==0){
//		adcOk= AdcAdpt_ChkStOk(1000) ;
//		comOk=CommuAdpt_ChkTxStOk(1000);
//		////////////////////////////////////////
////		comOk=SynReply_ChkStOk();
////		extern uint8_t MBCommuAdpt_ChkStOk(void);
////		if(comOk==0) comOk= MBCommuAdpt_ChkStOk();
//		////////////////////////////////////////////
//		trigStp=1;
//	}
//	if(uwTick< nexChkTick ) return ;
//	
//	if( adcOk==0 ){
//		extern IWDG_HandleTypeDef hiwdg;
//		HAL_IWDG_Refresh(&hiwdg);	

//		///HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_15);	
//		if(comOk==0){
//			if(trigStp==1) { DfLedOn();trigStp++;		nexChkTick+= 30;}
//			else if(trigStp==2) { DfLedOff();trigStp++; nexChkTick+= 50;}
//			else if(trigStp==3) { DfLedOn();trigStp++;	nexChkTick+= 30;}
//			else { DfLedOff();trigStp=0;				nexChkTick+= 500;}
//		}
//		else{
//			if(trigStp==1) {DfLedOn();trigStp++;	nexChkTick+= 200;}
//			else { 			DfLedOff();trigStp=0;	nexChkTick+= 410;}
//		}
//	}else {
//		if(comOk==0){
//			if(trigStp==1) { DfLedOn();trigStp++;	nexChkTick+= 510;}
//			else { DfLedOff(); trigStp=0;nexChkTick+= 100;}
//		}
//		else{
//			DfLedOn();
//			nexChkTick+= 500;
//		}
//	}
//}

static __IO uint8_t gNeedOp=0;
void ToTrigSaveAppCfg()
{
	gNeedOp=1;
}
uint8_t IsNeedSaveAppCfg(void)
{
	return gNeedOp;
}

void UsrAppMain_InitHw(void)
{
	#if DfF407Lan9252TstBoard==0
	AppCfg_Init();
	#endif
	
	AdcAdpt_InitAdcs();
	SynReply_Init();												
	
	#if DfEnModbusRTU
		extern void MBCommuAdpt_Init(uint8_t slvAddr);		
	MBCommuAdpt_Init( NodCfgs.nodId );			
	#endif
}

void UsrAppMain_LoopExe(void)
{
	static uint32_t canLstTick=0;
	static uint8_t msg[]={1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16};
	if(uwTick>canLstTick){
		canLstTick= uwTick+ 200;
	}

	
	// ADC娴嬭瘯鍑芥暟璋冪敤
//	UsrAppMain_AdcTest();
		
	
	#if DfF407Lan9252TstBoard==0
//	UpAppSt();
	#if DfUseEcat_DPortEsc==0 
	SynReply_Exe();													
	#endif
	
	////CommuAdpt_Exe();
	if(gNeedOp!=0){
		if(gNeedOp==1) {AppCfg_SavePrm(); AppCfg_SaveDecplNNMat();}
		gNeedOp=0;
	}
	if(NodCfgs.commMod && !SynReply_IsDebugMode ){
		#if DfEnPclRuiErMan48aa==0 && DfEnPclDeYi_XinJingChengMb_BFloat==0 && DfEnPclZhongShanWuYu_YueJiangMb==0
		RtmVlntPdo_Exe();
		#endif
	}
	#endif
	
	
}

float f32mod(float a, float b)
{
	if(b<1e-6) return 0;
	 
	float rem= a- ( b* (int64_t)(a/b));
	return rem;
}

float ValDivision_GetDivedFloat(float wtf, float deltf)
{
	static float oldWtf=0;
	float mdf=0, md100f=0;
	
	if(deltf<=1e-6) return wtf;
	
	mdf = f32mod( wtf,  deltf);
	md100f= mdf*100;
	if( md100f>= (deltf*65)){
		wtf += deltf- mdf;
	}
	else if( md100f<= (deltf*35)){
		wtf +=  -mdf;
	}
	else{
		if( oldWtf>= (wtf + deltf- mdf) ) {
			wtf+= deltf -mdf;
		}
		else{
			wtf+= -mdf;
		}
	}
	oldWtf= wtf;
	return wtf;
}

void Matrix_Transpose(float * orgMat, float * retMat, int matRws, int matClns) {
	for (int i = 0; i < matRws; i++) {
		for (int j = 0; j < matClns; j++) {	
			retMat[j*matRws + i] = orgMat[i*matClns + j];
		}
	}
}
 
void Matrix_Multiply(float* matLft, float* matRght, float* matRet, int matLftRws, int matLftClns, int matRghtRws, int matRghtClns) {
	if( matLftClns != matRghtRws ) return ;
	
	for (int i = 0; i < matLftRws; i++) {						
		for (int j = 0; j < matRghtClns; j++) {
			matRet[i*matRghtClns + j] = 0;		
			for (int p = 0; p < matLftClns; p++) {
				matRet[i*matRghtClns + j] += matLft[i*matLftClns + p] * matRght[p*matRghtClns + j];
			}
		}
	}
}
 

void Matrix_Add(float * matLft, float * matRght, float * matRet, int matRws, int matClns)
{
	for (int i = 0; i < matRws; i++) {					
		for (int j = 0; j < matClns; j++) {				
			matRet[i*matClns + j] = matLft[i*matClns + j] + matRght[i*matClns + j];
		}
	}
}

int Matrix_GetDeterm(int n, float arcs[n][n])				
{
	float temp[n][n];
	if (n == 1)
	{
		return arcs[0][0];
	}
	int ans = 0;
	
	int i, j, k;
	for (i = 0; i<n; i++)
	{
		for (j = 0; j<n - 1; j++)
		{
			for (k = 0; k<n - 1; k++)
			{
				temp[j][k] = arcs[j + 1][(k >= i) ? k + 1 : k];
 
			}
		}
		int t = Matrix_GetDeterm(n-1,temp);
		if (i % 2 == 0)
		{
			ans += arcs[0][i] * t;
		}
		else
		{
			ans -= arcs[0][i] * t;
		}
	}
	return ans;
}


void Matrix_Cofactors(int n,float arcs[n][n],  float ans[n][n])
{
	float temp[n][n];
	if (n == 1)
	{
		ans[0][0] = 1;
		return;
	}
	int i, j, k, t;
	for (i = 0; i<n; i++)
	{
		for (j = 0; j<n; j++)
		{
			for (k = 0; k<n - 1; k++)
			{
				for (t = 0; t<n - 1; t++)
				{
					temp[k][t] = arcs[k >= i ? k + 1 : k][t >= j ? t + 1 : t];
				}
			}
 
 
			ans[i][j] = Matrix_GetDeterm( n - 1, temp);
			if ((i + j) % 2 == 1)
			{
				ans[i][j] = -ans[i][j];
			}
		}
	}
}

void Matrix_Inversion(int n, float arcs[n][n],  float ans[n][n]) {
	int a = Matrix_GetDeterm(n, arcs );
	if (a == 0)
	{
		//printf("can not transform!\n");
	}
	else
	{
		float astar[n][n];
		Matrix_Cofactors( n, arcs, astar);
		for (int i = 0; i<n; i++)
		{
			for (int j = 0; j<n; j++)
			{
				ans[i][j] = (double)astar[i][j] / a;
				//printf("%.3lf ", (double)astar[i][j] / a);
				//printf("ans[i][j]=%.3lf\n", ans[i][j]);
			}
			//printf("\n");
		}
	}
}

//// ADC娴嬭瘯鍑芥暟瀹炵幇
//void UsrAppMain_AdcTest(void)
//{
//	static uint32_t adcTestTick = 0;
//	static uint8_t adcTestPhase = 0;
//	uint8_t adcTestResult = 0;
//	char testMsg[100];
//	
//	// 姣�3绉掓墽琛屼竴娆DC娴嬭瘯
//	if(uwTick > adcTestTick + 3000) {
//		adcTestTick = uwTick;
//		
//		switch(adcTestPhase) {
//			case 0:
//				// 娴嬭瘯ADC鐘舵€�
//				adcTestResult = AdcAdpt_ChkStOk(1000);
//				if(adcTestResult == 0) {
//					CommuAdpt_StaticWrite((uint8_t*)"ADC Status: OK\r\n", 16);
//				} else {
//					CommuAdpt_StaticWrite((uint8_t*)"ADC Status: TIMEOUT\r\n", 21);
//				}
//				adcTestPhase = 1;
//				break;
//				
//			case 1:
//				// 鏄剧ずADC鏃堕棿鎴宠皟璇曚俊鎭�
//				extern uint32_t gAdcAdpt_6ChlAdTrigTick;
//				sprintf(testMsg, "ADC Tick: %lu, Now: %lu\r\n", 
//					gAdcAdpt_6ChlAdTrigTick, uwTick);
//				CommuAdpt_StaticWrite((uint8_t*)testMsg, strlen(testMsg));
//				adcTestPhase = 2;
//				break;
//				
//			case 2:
//				// 娴嬭瘯ADC鏁版嵁璇诲彇 - 鍓�3涓€氶亾  
//				extern int16_t SenChlsCurCods[6];
//				sprintf(testMsg, "ADC Ch0-2: %d,%d,%d\r\n", 
//					SenChlsCurCods[0], SenChlsCurCods[1], SenChlsCurCods[2]);
//				CommuAdpt_StaticWrite((uint8_t*)testMsg, strlen(testMsg));
//				adcTestPhase = 3;
//				break;
//				
//			case 3:
//				// 娴嬭瘯ADC鏁版嵁璇诲彇 - 鍚�3涓€氶亾
//				extern int16_t SenChlsCurCods[6];
//				sprintf(testMsg, "ADC Ch3-5: %d,%d,%d\r\n", 
//					SenChlsCurCods[3], SenChlsCurCods[4], SenChlsCurCods[5]);
//				CommuAdpt_StaticWrite((uint8_t*)testMsg, strlen(testMsg));
//				adcTestPhase = 4;
//				break;
//				
//			case 4:
//				// 鏄剧ず閫氫俊寤惰繜淇℃伅
//				uint32_t commDelayUs = CommuAdpt_GetLastCommDelayUs();
//				sprintf(testMsg, "Comm Delay: %lu us\r\n", commDelayUs);
//				CommuAdpt_StaticWrite((uint8_t*)testMsg, strlen(testMsg));
//				adcTestPhase = 5;
//				break;
//				
//			case 5:
//				// 鍙戦€佸垎闅旂嚎
//				CommuAdpt_StaticWrite((uint8_t*)"--- ADC Test End ---\r\n", 22);
//				adcTestPhase = 0;  // 閲嶇疆寰幆
//				break;
//		}
//	}
//}

//// ADC璇︾粏娴嬭瘯鍑芥暟 - 鍙€氳繃485鎵嬪姩瑙﹀彂
//void UsrAppMain_AdcDetailedTest(void) 
//{
//	char testMsg[100];
//	
//	// 鍙戦€佽缁嗙殑ADC娴嬭瘯淇℃伅
//	CommuAdpt_StaticWrite((uint8_t*)"=== ADC Detailed Test ===\r\n", 28);
//	
//	// 寮哄埗鍒锋柊ADC鐘舵€佹椂闂存埑
//	extern uint32_t gAdcAdpt_6ChlAdTrigTick;
//	gAdcAdpt_6ChlAdTrigTick = uwTick;
//	CommuAdpt_StaticWrite((uint8_t*)"ADC Tick Reset\r\n", 16);
//	
//	// 娴嬭瘯SPI閫氫俊鐘舵€�
//	extern SPI_HandleTypeDef hspi2, hspi3;
//	if(hspi2.State == HAL_SPI_STATE_READY) {
//		CommuAdpt_StaticWrite((uint8_t*)"SPI2 (ADC0): READY\r\n", 20);
//	} else {
//		sprintf(testMsg, "SPI2 State: %d\r\n", hspi2.State);
//		CommuAdpt_StaticWrite((uint8_t*)testMsg, strlen(testMsg));
//	}
//	
//	if(hspi3.State == HAL_SPI_STATE_READY) {
//		CommuAdpt_StaticWrite((uint8_t*)"SPI3 (ADC1): READY\r\n", 20);
//	} else {
//		sprintf(testMsg, "SPI3 State: %d\r\n", hspi3.State);
//		CommuAdpt_StaticWrite((uint8_t*)testMsg, strlen(testMsg));
//	}
//	
//	// 鍙戦€佸綋鍓岮DC鏁版嵁
//	extern int16_t SenChlsCurCods[6];
//	for(int i = 0; i < 6; i++) {
//		sprintf(testMsg, "ADC Ch%d: %d\r\n", i, SenChlsCurCods[i]);
//		CommuAdpt_StaticWrite((uint8_t*)testMsg, strlen(testMsg));
//	}
//	
//	CommuAdpt_StaticWrite((uint8_t*)"=== Test Complete ===\r\n", 24);
//}

//// 鎵嬪姩瑙﹀彂ADC鍒濆鍖栨祴璇�
//void UsrAppMain_ForceAdcInit(void)
//{
//	CommuAdpt_StaticWrite((uint8_t*)"=== Force ADC Init ===\r\n", 24);
//	
//	// 閲嶆柊璋冪敤ADC鍒濆鍖�
//	extern void AdcAdpt_InitAdcs(void);
//	AdcAdpt_InitAdcs();
//	
//	CommuAdpt_StaticWrite((uint8_t*)"ADC ReInit Complete\r\n", 21);
//}



 


